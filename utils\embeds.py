import discord
from datetime import datetime
import random
from config import *

class EmbedCreator:
    @staticmethod
    def create_success_embed(title: str, description: str, user: discord.Member = None) -> discord.Embed:
        embed = discord.Embed(
            title=f"{EMBED_ICONS['success']} {title}",
            description=description,
            color=SUCCESS_COLOR,
            timestamp=datetime.utcnow()
        )
        embed.set_footer(text="ModCore • Success", icon_url="https://cdn.discordapp.com/emojis/1234567890123456789.png")
        if user:
            embed.set_thumbnail(url=user.display_avatar.url)
        return embed
    
    @staticmethod
    def create_error_embed(title: str, description: str) -> discord.Embed:
        embed = discord.Embed(
            title=f"{EMBED_ICONS['error']} {title}",
            description=description,
            color=ERROR_COLOR,
            timestamp=datetime.utcnow()
        )
        embed.set_footer(text="ModCore • Error", icon_url="https://cdn.discordapp.com/emojis/1234567890123456789.png")
        return embed
    
    @staticmethod
    def create_warning_embed(title: str, description: str) -> discord.Embed:
        embed = discord.Embed(
            title=f"{EMBED_ICONS['warning']} {title}",
            description=description,
            color=WARNING_COLOR,
            timestamp=datetime.utcnow()
        )
        embed.set_footer(text="ModCore • Warning", icon_url="https://cdn.discordapp.com/emojis/1234567890123456789.png")
        return embed
    
    @staticmethod
    def create_info_embed(title: str, description: str) -> discord.Embed:
        embed = discord.Embed(
            title=f"{EMBED_ICONS['info']} {title}",
            description=description,
            color=INFO_COLOR,
            timestamp=datetime.utcnow()
        )
        embed.set_footer(text="ModCore • Information", icon_url="https://cdn.discordapp.com/emojis/1234567890123456789.png")
        return embed
    
    @staticmethod
    def create_moderation_embed(action: str, user: discord.Member, moderator: discord.Member, 
                               reason: str = "No reason provided", duration: str = None) -> discord.Embed:
        colors = {
            'warn': WARNING_COLOR,
            'mute': 0xFFA500,
            'kick': 0xFF4500,
            'ban': ERROR_COLOR,
            'unban': SUCCESS_COLOR,
            'unmute': SUCCESS_COLOR
        }
        
        embed = discord.Embed(
            title=f"{EMBED_ICONS['moderation']} {action.title()} • {user}",
            color=colors.get(action.lower(), EMBED_COLOR),
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 User", value=f"{user.mention}\n`{user.id}`", inline=True)
        embed.add_field(name="🔨 Moderator", value=f"{moderator.mention}\n`{moderator.id}`", inline=True)
        embed.add_field(name="📝 Reason", value=reason, inline=False)
        
        if duration:
            embed.add_field(name="⏰ Duration", value=duration, inline=True)
        
        embed.set_thumbnail(url=user.display_avatar.url)
        embed.set_footer(text=f"ModCore • {action.title()}", icon_url=moderator.display_avatar.url)
        
        return embed
    
    @staticmethod
    def create_automod_embed(violation_type: str, user: discord.Member, action: str, 
                            content: str = None) -> discord.Embed:
        embed = discord.Embed(
            title=f"{EMBED_ICONS['automod']} AutoMod Violation",
            description=f"**Violation:** {violation_type}\n**Action:** {action}",
            color=0xFF6B6B,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 User", value=f"{user.mention}\n`{user.id}`", inline=True)
        embed.add_field(name="📍 Channel", value=f"<#{user.voice.channel.id if user.voice else 'Unknown'}>", inline=True)
        
        if content and len(content) <= 1024:
            embed.add_field(name="📝 Content", value=f"```{content[:1000]}```", inline=False)
        
        embed.set_thumbnail(url=user.display_avatar.url)
        embed.set_footer(text="ModCore • AutoMod", icon_url="https://cdn.discordapp.com/emojis/1234567890123456789.png")
        
        return embed
    
    @staticmethod
    def create_giveaway_embed(prize: str, host: discord.Member, winners: int, 
                             end_time: str, entries: int = 0) -> discord.Embed:
        embed = discord.Embed(
            title=f"{EMBED_ICONS['giveaway']} GIVEAWAY",
            description=f"**Prize:** {prize}\n\n**React with 🎉 to enter!**",
            color=0xFF69B4,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="🎯 Winners", value=str(winners), inline=True)
        embed.add_field(name="👥 Entries", value=str(entries), inline=True)
        embed.add_field(name="⏰ Ends", value=end_time, inline=True)
        embed.add_field(name="🎪 Hosted by", value=host.mention, inline=False)
        
        embed.set_thumbnail(url="https://cdn.discordapp.com/emojis/1234567890123456789.png")
        embed.set_footer(text="ModCore • Giveaway System", icon_url=host.display_avatar.url)
        
        return embed
    
    @staticmethod
    def create_starboard_embed(message: discord.Message, star_count: int) -> discord.Embed:
        embed = discord.Embed(
            description=message.content or "*No text content*",
            color=0xFFD700,
            timestamp=message.created_at
        )
        
        embed.set_author(
            name=f"{message.author.display_name}",
            icon_url=message.author.display_avatar.url
        )
        
        embed.add_field(
            name="📍 Source",
            value=f"[Jump to message]({message.jump_url})",
            inline=True
        )
        
        embed.add_field(
            name="⭐ Stars",
            value=str(star_count),
            inline=True
        )
        
        if message.attachments:
            embed.set_image(url=message.attachments[0].url)
        
        embed.set_footer(text=f"#{message.channel.name} • ModCore Starboard")
        
        return embed
    
    @staticmethod
    def create_welcome_embed(member: discord.Member, guild: discord.Guild, 
                           custom_message: str = None) -> discord.Embed:
        messages = [
            f"Welcome to **{guild.name}**, {member.mention}! 🎉",
            f"A wild {member.mention} appeared! Welcome to **{guild.name}**! ✨",
            f"Everyone welcome {member.mention} to **{guild.name}**! 🌟",
            f"{member.mention} just joined the party! Welcome to **{guild.name}**! 🎊"
        ]
        
        description = custom_message or random.choice(messages)
        
        embed = discord.Embed(
            title=f"{EMBED_ICONS['welcome']} Member Joined",
            description=description,
            color=SUCCESS_COLOR,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 User", value=f"{member.mention}\n`{member.id}`", inline=True)
        embed.add_field(name="📊 Member Count", value=f"{guild.member_count}", inline=True)
        embed.add_field(name="📅 Account Created", value=f"<t:{int(member.created_at.timestamp())}:R>", inline=True)
        
        embed.set_thumbnail(url=member.display_avatar.url)
        embed.set_footer(text=f"ModCore • Welcome to {guild.name}", icon_url=guild.icon.url if guild.icon else None)
        
        return embed
    
    @staticmethod
    def create_goodbye_embed(member: discord.Member, guild: discord.Guild, 
                           custom_message: str = None) -> discord.Embed:
        messages = [
            f"Goodbye {member.display_name}! Thanks for being part of **{guild.name}**! 👋",
            f"{member.display_name} has left **{guild.name}**. We'll miss you! 😢",
            f"Farewell {member.display_name}! Hope to see you again in **{guild.name}**! 🌅"
        ]
        
        description = custom_message or random.choice(messages)
        
        embed = discord.Embed(
            title=f"{EMBED_ICONS['goodbye']} Member Left",
            description=description,
            color=0xFF6B6B,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 User", value=f"{member.display_name}\n`{member.id}`", inline=True)
        embed.add_field(name="📊 Member Count", value=f"{guild.member_count}", inline=True)
        embed.add_field(name="⏰ Joined", value=f"<t:{int(member.joined_at.timestamp())}:R>" if member.joined_at else "Unknown", inline=True)
        
        embed.set_thumbnail(url=member.display_avatar.url)
        embed.set_footer(text=f"ModCore • Goodbye from {guild.name}", icon_url=guild.icon.url if guild.icon else None)
        
        return embed
    
    @staticmethod
    def create_server_info_embed(guild: discord.Guild) -> discord.Embed:
        embed = discord.Embed(
            title=f"{EMBED_ICONS['utility']} Server Information",
            description=f"**{guild.name}**",
            color=EMBED_COLOR,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="🆔 Server ID", value=f"`{guild.id}`", inline=True)
        embed.add_field(name="👑 Owner", value=f"{guild.owner.mention if guild.owner else 'Unknown'}", inline=True)
        embed.add_field(name="📅 Created", value=f"<t:{int(guild.created_at.timestamp())}:F>", inline=True)
        
        embed.add_field(name="👥 Members", value=f"{guild.member_count}", inline=True)
        embed.add_field(name="💬 Channels", value=f"{len(guild.channels)}", inline=True)
        embed.add_field(name="🎭 Roles", value=f"{len(guild.roles)}", inline=True)
        
        embed.add_field(name="😀 Emojis", value=f"{len(guild.emojis)}", inline=True)
        embed.add_field(name="🔒 Verification", value=f"{guild.verification_level.name.title()}", inline=True)
        embed.add_field(name="🛡️ Boost Level", value=f"Level {guild.premium_tier}", inline=True)
        
        if guild.icon:
            embed.set_thumbnail(url=guild.icon.url)
        
        embed.set_footer(text="ModCore • Server Information")
        
        return embed
    
    @staticmethod
    def create_user_info_embed(user: discord.Member) -> discord.Embed:
        embed = discord.Embed(
            title=f"{EMBED_ICONS['utility']} User Information",
            description=f"**{user.display_name}**",
            color=user.color if user.color != discord.Color.default() else EMBED_COLOR,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="🆔 User ID", value=f"`{user.id}`", inline=True)
        embed.add_field(name="📅 Account Created", value=f"<t:{int(user.created_at.timestamp())}:F>", inline=True)
        embed.add_field(name="📅 Joined Server", value=f"<t:{int(user.joined_at.timestamp())}:F>" if user.joined_at else "Unknown", inline=True)
        
        embed.add_field(name="🎭 Roles", value=f"{len(user.roles) - 1}", inline=True)
        embed.add_field(name="🔝 Top Role", value=user.top_role.mention if user.top_role.name != "@everyone" else "None", inline=True)
        embed.add_field(name="🤖 Bot", value="Yes" if user.bot else "No", inline=True)
        
        if user.premium_since:
            embed.add_field(name="💎 Boosting Since", value=f"<t:{int(user.premium_since.timestamp())}:F>", inline=True)
        
        embed.set_thumbnail(url=user.display_avatar.url)
        embed.set_footer(text="ModCore • User Information")
        
        return embed
