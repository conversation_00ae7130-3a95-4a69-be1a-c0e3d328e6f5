import aiosqlite
import asyncio
from typing import Optional, List, Dict, Any

class Database:
    def __init__(self, db_path: str):
        self.db_path = db_path
        
    async def init_db(self):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                CREATE TABLE IF NOT EXISTS guild_settings (
                    guild_id INTEGER PRIMARY KEY,
                    prefix TEXT DEFAULT '?',
                    mod_log_channel INTEGER,
                    welcome_channel INTEGER,
                    welcome_message TEXT,
                    goodbye_message TEXT,
                    automod_enabled BOOLEAN DEFAULT 1,
                    starboard_channel INTEGER,
                    starboard_threshold INTEGER DEFAULT 3
                )
            ''')
            
            await db.execute('''
                CREATE TABLE IF NOT EXISTS automod_settings (
                    guild_id INTEGER PRIMARY KEY,
                    spam_enabled BOOLEAN DEFAULT 1,
                    links_enabled BOOLEAN DEFAULT 0,
                    caps_enabled BOOLEAN DEFAULT 1,
                    mentions_enabled BOOLEAN DEFAULT 1,
                    duplicates_enabled BOOLEAN DEFAULT 1,
                    banned_words TEXT,
                    whitelisted_links TEXT,
                    punishment_type TEXT DEFAULT 'warn'
                )
            ''')
            
            await db.execute('''
                CREATE TABLE IF NOT EXISTS warnings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    guild_id INTEGER,
                    user_id INTEGER,
                    moderator_id INTEGER,
                    reason TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            await db.execute('''
                CREATE TABLE IF NOT EXISTS mutes (
                    guild_id INTEGER,
                    user_id INTEGER,
                    end_time DATETIME,
                    PRIMARY KEY (guild_id, user_id)
                )
            ''')
            
            await db.execute('''
                CREATE TABLE IF NOT EXISTS reaction_roles (
                    guild_id INTEGER,
                    message_id INTEGER,
                    emoji TEXT,
                    role_id INTEGER,
                    PRIMARY KEY (guild_id, message_id, emoji)
                )
            ''')
            
            await db.execute('''
                CREATE TABLE IF NOT EXISTS giveaways (
                    message_id INTEGER PRIMARY KEY,
                    guild_id INTEGER,
                    channel_id INTEGER,
                    host_id INTEGER,
                    prize TEXT,
                    winners INTEGER,
                    end_time DATETIME,
                    ended BOOLEAN DEFAULT 0
                )
            ''')
            
            await db.execute('''
                CREATE TABLE IF NOT EXISTS custom_commands (
                    guild_id INTEGER,
                    command_name TEXT,
                    response TEXT,
                    enabled BOOLEAN DEFAULT 1,
                    PRIMARY KEY (guild_id, command_name)
                )
            ''')
            
            await db.execute('''
                CREATE TABLE IF NOT EXISTS starboard_messages (
                    original_message_id INTEGER PRIMARY KEY,
                    starboard_message_id INTEGER,
                    guild_id INTEGER,
                    channel_id INTEGER,
                    star_count INTEGER DEFAULT 0
                )
            ''')
            
            await db.commit()
    
    async def get_guild_settings(self, guild_id: int) -> Dict[str, Any]:
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT * FROM guild_settings WHERE guild_id = ?', 
                (guild_id,)
            ) as cursor:
                row = await cursor.fetchone()
                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                else:
                    await db.execute(
                        'INSERT INTO guild_settings (guild_id) VALUES (?)',
                        (guild_id,)
                    )
                    await db.commit()
                    return {
                        'guild_id': guild_id,
                        'prefix': '?',
                        'mod_log_channel': None,
                        'welcome_channel': None,
                        'welcome_message': None,
                        'goodbye_message': None,
                        'automod_enabled': True,
                        'starboard_channel': None,
                        'starboard_threshold': 3
                    }
    
    async def update_guild_setting(self, guild_id: int, setting: str, value: Any):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                f'UPDATE guild_settings SET {setting} = ? WHERE guild_id = ?',
                (value, guild_id)
            )
            await db.commit()
    
    async def add_warning(self, guild_id: int, user_id: int, moderator_id: int, reason: str):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                'INSERT INTO warnings (guild_id, user_id, moderator_id, reason) VALUES (?, ?, ?, ?)',
                (guild_id, user_id, moderator_id, reason)
            )
            await db.commit()
    
    async def get_warnings(self, guild_id: int, user_id: int) -> List[Dict[str, Any]]:
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT * FROM warnings WHERE guild_id = ? AND user_id = ? ORDER BY timestamp DESC',
                (guild_id, user_id)
            ) as cursor:
                rows = await cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
    
    async def remove_warning(self, warning_id: int):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('DELETE FROM warnings WHERE id = ?', (warning_id,))
            await db.commit()
    
    async def add_reaction_role(self, guild_id: int, message_id: int, emoji: str, role_id: int):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                'INSERT OR REPLACE INTO reaction_roles VALUES (?, ?, ?, ?)',
                (guild_id, message_id, emoji, role_id)
            )
            await db.commit()
    
    async def get_reaction_role(self, guild_id: int, message_id: int, emoji: str) -> Optional[int]:
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT role_id FROM reaction_roles WHERE guild_id = ? AND message_id = ? AND emoji = ?',
                (guild_id, message_id, emoji)
            ) as cursor:
                row = await cursor.fetchone()
                return row[0] if row else None
    
    async def remove_reaction_role(self, guild_id: int, message_id: int, emoji: str):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                'DELETE FROM reaction_roles WHERE guild_id = ? AND message_id = ? AND emoji = ?',
                (guild_id, message_id, emoji)
            )
            await db.commit()
    
    async def add_giveaway(self, message_id: int, guild_id: int, channel_id: int, 
                          host_id: int, prize: str, winners: int, end_time: str):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                'INSERT INTO giveaways VALUES (?, ?, ?, ?, ?, ?, ?, 0)',
                (message_id, guild_id, channel_id, host_id, prize, winners, end_time)
            )
            await db.commit()
    
    async def get_active_giveaways(self) -> List[Dict[str, Any]]:
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT * FROM giveaways WHERE ended = 0 AND end_time <= datetime("now")'
            ) as cursor:
                rows = await cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
    
    async def end_giveaway(self, message_id: int):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                'UPDATE giveaways SET ended = 1 WHERE message_id = ?',
                (message_id,)
            )
            await db.commit()
    
    async def add_custom_command(self, guild_id: int, command_name: str, response: str):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                'INSERT OR REPLACE INTO custom_commands VALUES (?, ?, ?, 1)',
                (guild_id, command_name, response)
            )
            await db.commit()
    
    async def get_custom_command(self, guild_id: int, command_name: str) -> Optional[str]:
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT response FROM custom_commands WHERE guild_id = ? AND command_name = ? AND enabled = 1',
                (guild_id, command_name)
            ) as cursor:
                row = await cursor.fetchone()
                return row[0] if row else None
    
    async def remove_custom_command(self, guild_id: int, command_name: str):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                'DELETE FROM custom_commands WHERE guild_id = ? AND command_name = ?',
                (guild_id, command_name)
            )
            await db.commit()
    
    async def get_automod_settings(self, guild_id: int) -> Dict[str, Any]:
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT * FROM automod_settings WHERE guild_id = ?',
                (guild_id,)
            ) as cursor:
                row = await cursor.fetchone()
                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                else:
                    await db.execute(
                        'INSERT INTO automod_settings (guild_id) VALUES (?)',
                        (guild_id,)
                    )
                    await db.commit()
                    return {
                        'guild_id': guild_id,
                        'spam_enabled': True,
                        'links_enabled': False,
                        'caps_enabled': True,
                        'mentions_enabled': True,
                        'duplicates_enabled': True,
                        'banned_words': '',
                        'whitelisted_links': '',
                        'punishment_type': 'warn'
                    }
    
    async def update_automod_setting(self, guild_id: int, setting: str, value: Any):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                f'UPDATE automod_settings SET {setting} = ? WHERE guild_id = ?',
                (value, guild_id)
            )
            await db.commit()
