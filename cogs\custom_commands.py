import discord
from discord.ext import commands
from datetime import datetime

from utils.embeds import EmbedCreator
from utils.permissions import is_moderator

class CustomCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
    
    @commands.command(name='addcommand', aliases=['addcmd'])
    @is_moderator()
    async def add_custom_command(self, ctx, command_name: str, *, response: str):
        if command_name.lower() in [cmd.name for cmd in self.bot.commands]:
            embed = EmbedCreator.create_error_embed(
                "Command Already Exists",
                f"A built-in command named `{command_name}` already exists."
            )
            await ctx.send(embed=embed)
            return
        
        if len(command_name) > 32:
            embed = EmbedCreator.create_error_embed(
                "Command Name Too Long",
                "Command names cannot be longer than 32 characters."
            )
            await ctx.send(embed=embed)
            return
        
        if len(response) > 2000:
            embed = EmbedCreator.create_error_embed(
                "Response Too Long",
                "Command responses cannot be longer than 2000 characters."
            )
            await ctx.send(embed=embed)
            return
        
        await self.db.add_custom_command(ctx.guild.id, command_name.lower(), response)
        
        embed = EmbedCreator.create_success_embed(
            "Custom Command Added",
            f"Successfully created custom command: `{command_name}`\n\n**Response Preview:**\n{response[:100]}{'...' if len(response) > 100 else ''}"
        )
        
        embed.add_field(
            name="📝 Available Variables",
            value="`{user}`, `{user.name}`, `{user.id}`, `{guild}`, `{guild.id}`, `{channel}`, `{channel.name}`, `{channel.id}`",
            inline=False
        )
        
        await ctx.send(embed=embed)
    
    @commands.command(name='removecommand', aliases=['delcommand', 'deletecmd'])
    @is_moderator()
    async def remove_custom_command(self, ctx, command_name: str):
        existing_command = await self.db.get_custom_command(ctx.guild.id, command_name.lower())
        
        if not existing_command:
            embed = EmbedCreator.create_error_embed(
                "Command Not Found",
                f"No custom command named `{command_name}` found."
            )
            await ctx.send(embed=embed)
            return
        
        await self.db.remove_custom_command(ctx.guild.id, command_name.lower())
        
        embed = EmbedCreator.create_success_embed(
            "Custom Command Removed",
            f"Successfully removed custom command: `{command_name}`"
        )
        await ctx.send(embed=embed)
    
    @commands.command(name='editcommand', aliases=['editcmd'])
    @is_moderator()
    async def edit_custom_command(self, ctx, command_name: str, *, new_response: str):
        existing_command = await self.db.get_custom_command(ctx.guild.id, command_name.lower())
        
        if not existing_command:
            embed = EmbedCreator.create_error_embed(
                "Command Not Found",
                f"No custom command named `{command_name}` found."
            )
            await ctx.send(embed=embed)
            return
        
        if len(new_response) > 2000:
            embed = EmbedCreator.create_error_embed(
                "Response Too Long",
                "Command responses cannot be longer than 2000 characters."
            )
            await ctx.send(embed=embed)
            return
        
        await self.db.add_custom_command(ctx.guild.id, command_name.lower(), new_response)
        
        embed = EmbedCreator.create_success_embed(
            "Custom Command Updated",
            f"Successfully updated custom command: `{command_name}`\n\n**New Response Preview:**\n{new_response[:100]}{'...' if len(new_response) > 100 else ''}"
        )
        await ctx.send(embed=embed)
    
    @commands.command(name='commands', aliases=['customcommands', 'cmdlist'])
    async def list_custom_commands(self, ctx):
        import aiosqlite
        
        async with aiosqlite.connect(self.db.db_path) as db:
            async with db.execute(
                'SELECT command_name, response FROM custom_commands WHERE guild_id = ? AND enabled = 1',
                (ctx.guild.id,)
            ) as cursor:
                commands = await cursor.fetchall()
        
        if not commands:
            embed = EmbedCreator.create_info_embed(
                "No Custom Commands",
                "This server has no custom commands.\n\nUse `?addcommand <name> <response>` to create one!"
            )
            await ctx.send(embed=embed)
            return
        
        embed = discord.Embed(
            title="📝 Custom Commands",
            description=f"This server has {len(commands)} custom command(s):",
            color=0x7289DA,
            timestamp=datetime.utcnow()
        )
        
        command_list = []
        for i, (name, response) in enumerate(commands[:20], 1):
            preview = response[:50] + "..." if len(response) > 50 else response
            command_list.append(f"`{i}.` **{name}** - {preview}")
        
        embed.add_field(
            name="📋 Command List",
            value='\n'.join(command_list) if command_list else "No commands found",
            inline=False
        )
        
        if len(commands) > 20:
            embed.set_footer(text=f"Showing 20 of {len(commands)} commands")
        else:
            embed.set_footer(text=f"Total: {len(commands)} commands")
        
        embed.add_field(
            name="💡 Usage",
            value="Simply type the command name with your server prefix to use it!",
            inline=False
        )
        
        await ctx.send(embed=embed)
    
    @commands.command(name='commandinfo', aliases=['cmdinfo'])
    async def command_info(self, ctx, command_name: str):
        command_response = await self.db.get_custom_command(ctx.guild.id, command_name.lower())
        
        if not command_response:
            embed = EmbedCreator.create_error_embed(
                "Command Not Found",
                f"No custom command named `{command_name}` found."
            )
            await ctx.send(embed=embed)
            return
        
        embed = discord.Embed(
            title=f"📝 Command Info: {command_name}",
            color=0x7289DA,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(
            name="📋 Command Name",
            value=f"`{command_name}`",
            inline=True
        )
        
        embed.add_field(
            name="📏 Response Length",
            value=f"{len(command_response)} characters",
            inline=True
        )
        
        embed.add_field(
            name="🔧 Usage",
            value=f"`?{command_name}`",
            inline=True
        )
        
        embed.add_field(
            name="📝 Response",
            value=f"```{command_response[:1000]}{'...' if len(command_response) > 1000 else ''}```",
            inline=False
        )
        
        embed.set_footer(text="ModCore • Custom Command Information")
        await ctx.send(embed=embed)
    
    @commands.command(name='testcommand', aliases=['testcmd'])
    @is_moderator()
    async def test_custom_command(self, ctx, command_name: str):
        command_response = await self.db.get_custom_command(ctx.guild.id, command_name.lower())
        
        if not command_response:
            embed = EmbedCreator.create_error_embed(
                "Command Not Found",
                f"No custom command named `{command_name}` found."
            )
            await ctx.send(embed=embed)
            return
        
        variables = {
            '{user}': ctx.author.mention,
            '{user.name}': ctx.author.display_name,
            '{user.id}': str(ctx.author.id),
            '{guild}': ctx.guild.name,
            '{guild.id}': str(ctx.guild.id),
            '{channel}': ctx.channel.mention,
            '{channel.name}': ctx.channel.name,
            '{channel.id}': str(ctx.channel.id)
        }
        
        processed_response = command_response
        for var, value in variables.items():
            processed_response = processed_response.replace(var, value)
        
        embed = discord.Embed(
            title=f"🧪 Testing Command: {command_name}",
            description="**Command Output:**",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(
            name="📤 Result",
            value=processed_response[:1024] if len(processed_response) <= 1024 else processed_response[:1021] + "...",
            inline=False
        )
        
        if len(processed_response) > 1024:
            embed.set_footer(text="Output truncated for display")
        else:
            embed.set_footer(text="ModCore • Command Test")
        
        await ctx.send(embed=embed)
    
    @commands.command(name='variables', aliases=['vars'])
    async def show_variables(self, ctx):
        embed = discord.Embed(
            title="📝 Custom Command Variables",
            description="You can use these variables in your custom commands:",
            color=0x7289DA,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(
            name="👤 User Variables",
            value="`{user}` - Mentions the user\n`{user.name}` - User's display name\n`{user.id}` - User's ID",
            inline=False
        )
        
        embed.add_field(
            name="🏠 Guild Variables",
            value="`{guild}` - Server name\n`{guild.id}` - Server ID",
            inline=False
        )
        
        embed.add_field(
            name="📺 Channel Variables",
            value="`{channel}` - Mentions the channel\n`{channel.name}` - Channel name\n`{channel.id}` - Channel ID",
            inline=False
        )
        
        embed.add_field(
            name="💡 Example",
            value="**Command:** `Welcome {user} to {guild}!`\n**Output:** `Welcome @User to My Server!`",
            inline=False
        )
        
        embed.set_footer(text="ModCore • Variable Reference")
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(CustomCommands(bot))
