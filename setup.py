#!/usr/bin/env python3

import os
import sys
import subprocess

def install_requirements():
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements")
        sys.exit(1)

def setup_env():
    if not os.path.exists('.env'):
        print("📝 Creating .env file...")
        with open('.env.example', 'r') as example:
            content = example.read()
        
        with open('.env', 'w') as env_file:
            env_file.write(content)
        
        print("✅ .env file created")
        print("⚠️  Please edit .env and add your Discord bot token")
    else:
        print("✅ .env file already exists")

def create_directories():
    directories = ['database', 'utils', 'cogs']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")

def main():
    print("🔧 Setting up ModCore Discord Bot...")
    print("=" * 50)
    
    create_directories()
    install_requirements()
    setup_env()
    
    print("=" * 50)
    print("✅ Setup complete!")
    print()
    print("📋 Next steps:")
    print("1. Edit .env file and add your Discord bot token")
    print("2. Run the bot with: python run.py")
    print()
    print("🔗 Need help getting a bot token?")
    print("   Visit: https://discord.com/developers/applications")

if __name__ == "__main__":
    main()
