import os
import sys
import subprocess

def install_requirements():
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    except subprocess.CalledProcessError:
        sys.exit(1)

def setup_env():
    if not os.path.exists('.env'):
        with open('.env.example', 'r') as example:
            content = example.read()
        with open('.env', 'w') as env_file:
            env_file.write(content)

def create_directories():
    directories = ['database', 'utils', 'cogs']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)

def main():
    create_directories()
    install_requirements()
    setup_env()

if __name__ == "__main__":
    main()
