import discord
from discord.ext import commands
from datetime import datetime

from utils.embeds import EmbedCreator
from utils.permissions import is_moderator

class Starboard(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
    
    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        if str(payload.emoji) != '⭐':
            return
        
        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return
        
        settings = await self.db.get_guild_settings(guild.id)
        starboard_channel_id = settings.get('starboard_channel')
        starboard_threshold = settings.get('starboard_threshold', 3)
        
        if not starboard_channel_id:
            return
        
        starboard_channel = guild.get_channel(starboard_channel_id)
        if not starboard_channel:
            return
        
        channel = guild.get_channel(payload.channel_id)
        if not channel or channel == starboard_channel:
            return
        
        try:
            message = await channel.fetch_message(payload.message_id)
        except discord.NotFound:
            return
        
        if message.author.bot:
            return
        
        star_reaction = discord.utils.get(message.reactions, emoji='⭐')
        if not star_reaction or star_reaction.count < starboard_threshold:
            return
        
        import aiosqlite
        async with aiosqlite.connect(self.db.db_path) as db:
            async with db.execute(
                'SELECT starboard_message_id, star_count FROM starboard_messages WHERE original_message_id = ?',
                (message.id,)
            ) as cursor:
                existing = await cursor.fetchone()
        
        if existing:
            starboard_message_id, old_count = existing
            if star_reaction.count != old_count:
                try:
                    starboard_message = await starboard_channel.fetch_message(starboard_message_id)
                    embed = EmbedCreator.create_starboard_embed(message, star_reaction.count)
                    await starboard_message.edit(embed=embed)
                    
                    async with aiosqlite.connect(self.db.db_path) as db:
                        await db.execute(
                            'UPDATE starboard_messages SET star_count = ? WHERE original_message_id = ?',
                            (star_reaction.count, message.id)
                        )
                        await db.commit()
                except discord.NotFound:
                    pass
        else:
            embed = EmbedCreator.create_starboard_embed(message, star_reaction.count)
            starboard_message = await starboard_channel.send(embed=embed)
            
            async with aiosqlite.connect(self.db.db_path) as db:
                await db.execute(
                    'INSERT INTO starboard_messages VALUES (?, ?, ?, ?, ?)',
                    (message.id, starboard_message.id, guild.id, channel.id, star_reaction.count)
                )
                await db.commit()
    
    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        if str(payload.emoji) != '⭐':
            return
        
        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return
        
        settings = await self.db.get_guild_settings(guild.id)
        starboard_channel_id = settings.get('starboard_channel')
        starboard_threshold = settings.get('starboard_threshold', 3)
        
        if not starboard_channel_id:
            return
        
        starboard_channel = guild.get_channel(starboard_channel_id)
        if not starboard_channel:
            return
        
        channel = guild.get_channel(payload.channel_id)
        if not channel or channel == starboard_channel:
            return
        
        try:
            message = await channel.fetch_message(payload.message_id)
        except discord.NotFound:
            return
        
        star_reaction = discord.utils.get(message.reactions, emoji='⭐')
        star_count = star_reaction.count if star_reaction else 0
        
        import aiosqlite
        async with aiosqlite.connect(self.db.db_path) as db:
            async with db.execute(
                'SELECT starboard_message_id FROM starboard_messages WHERE original_message_id = ?',
                (message.id,)
            ) as cursor:
                existing = await cursor.fetchone()
        
        if existing:
            starboard_message_id = existing[0]
            
            if star_count < starboard_threshold:
                try:
                    starboard_message = await starboard_channel.fetch_message(starboard_message_id)
                    await starboard_message.delete()
                    
                    async with aiosqlite.connect(self.db.db_path) as db:
                        await db.execute(
                            'DELETE FROM starboard_messages WHERE original_message_id = ?',
                            (message.id,)
                        )
                        await db.commit()
                except discord.NotFound:
                    pass
            else:
                try:
                    starboard_message = await starboard_channel.fetch_message(starboard_message_id)
                    embed = EmbedCreator.create_starboard_embed(message, star_count)
                    await starboard_message.edit(embed=embed)
                    
                    async with aiosqlite.connect(self.db.db_path) as db:
                        await db.execute(
                            'UPDATE starboard_messages SET star_count = ? WHERE original_message_id = ?',
                            (star_count, message.id)
                        )
                        await db.commit()
                except discord.NotFound:
                    pass
    
    @commands.group(name='starboard', invoke_without_command=True)
    @is_moderator()
    async def starboard(self, ctx):
        settings = await self.db.get_guild_settings(ctx.guild.id)
        
        embed = discord.Embed(
            title="⭐ Starboard System",
            color=0xFFD700,
            timestamp=datetime.utcnow()
        )
        
        starboard_channel = None
        if settings.get('starboard_channel'):
            starboard_channel = ctx.guild.get_channel(settings['starboard_channel'])
        
        embed.add_field(
            name="📺 Starboard Channel",
            value=starboard_channel.mention if starboard_channel else "Not set",
            inline=True
        )
        
        embed.add_field(
            name="⭐ Star Threshold",
            value=str(settings.get('starboard_threshold', 3)),
            inline=True
        )
        
        embed.add_field(
            name="📊 Status",
            value="🟢 Active" if starboard_channel else "🔴 Inactive",
            inline=True
        )
        
        embed.add_field(
            name="🔧 Commands",
            value="`?starboard channel <#channel>`\n`?starboard threshold <number>`\n`?starboard disable`",
            inline=False
        )
        
        embed.add_field(
            name="ℹ️ How it works",
            value="When a message gets enough ⭐ reactions, it will be posted to the starboard channel.",
            inline=False
        )
        
        embed.set_footer(text="ModCore • Starboard System")
        await ctx.send(embed=embed)
    
    @starboard.command(name='channel')
    @is_moderator()
    async def set_starboard_channel(self, ctx, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        
        await self.db.update_guild_setting(ctx.guild.id, 'starboard_channel', channel.id)
        
        embed = EmbedCreator.create_success_embed(
            "Starboard Channel Set",
            f"Starred messages will now be posted to {channel.mention}."
        )
        await ctx.send(embed=embed)
        
        test_embed = discord.Embed(
            title="⭐ Starboard Test",
            description="This is a test message to confirm the starboard is working properly.",
            color=0xFFD700,
            timestamp=datetime.utcnow()
        )
        test_embed.set_footer(text="ModCore • Starboard System")
        
        await channel.send(embed=test_embed)
    
    @starboard.command(name='threshold')
    @is_moderator()
    async def set_starboard_threshold(self, ctx, threshold: int):
        if threshold < 1 or threshold > 50:
            embed = EmbedCreator.create_error_embed(
                "Invalid Threshold",
                "Starboard threshold must be between 1 and 50."
            )
            await ctx.send(embed=embed)
            return
        
        await self.db.update_guild_setting(ctx.guild.id, 'starboard_threshold', threshold)
        
        embed = EmbedCreator.create_success_embed(
            "Starboard Threshold Set",
            f"Messages now need **{threshold}** ⭐ reactions to be posted to the starboard."
        )
        await ctx.send(embed=embed)
    
    @starboard.command(name='disable')
    @is_moderator()
    async def disable_starboard(self, ctx):
        await self.db.update_guild_setting(ctx.guild.id, 'starboard_channel', None)
        
        embed = EmbedCreator.create_success_embed(
            "Starboard Disabled",
            "The starboard system has been disabled."
        )
        await ctx.send(embed=embed)
    
    @commands.command(name='star')
    async def manual_star(self, ctx, message_id: int):
        try:
            message = await ctx.channel.fetch_message(message_id)
        except discord.NotFound:
            embed = EmbedCreator.create_error_embed(
                "Message Not Found",
                "I couldn't find a message with that ID in this channel."
            )
            await ctx.send(embed=embed)
            return
        
        if message.author.bot:
            embed = EmbedCreator.create_error_embed(
                "Cannot Star Bot Messages",
                "Bot messages cannot be starred."
            )
            await ctx.send(embed=embed)
            return
        
        star_reaction = discord.utils.get(message.reactions, emoji='⭐')
        star_count = star_reaction.count if star_reaction else 0
        
        embed = EmbedCreator.create_starboard_embed(message, star_count)
        embed.title = "⭐ Manual Star"
        
        await ctx.send(embed=embed)
    
    @commands.command(name='stars')
    async def show_star_count(self, ctx, message_id: int = None):
        if message_id:
            try:
                message = await ctx.channel.fetch_message(message_id)
            except discord.NotFound:
                embed = EmbedCreator.create_error_embed(
                    "Message Not Found",
                    "I couldn't find a message with that ID in this channel."
                )
                await ctx.send(embed=embed)
                return
            
            star_reaction = discord.utils.get(message.reactions, emoji='⭐')
            star_count = star_reaction.count if star_reaction else 0
            
            embed = discord.Embed(
                title="⭐ Star Count",
                description=f"This message has **{star_count}** stars.",
                color=0xFFD700,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(name="🔗 Message", value=f"[Jump to message]({message.jump_url})", inline=True)
            embed.add_field(name="👤 Author", value=message.author.mention, inline=True)
            
            await ctx.send(embed=embed)
        else:
            import aiosqlite
            async with aiosqlite.connect(self.db.db_path) as db:
                async with db.execute(
                    'SELECT original_message_id, star_count FROM starboard_messages WHERE guild_id = ? ORDER BY star_count DESC LIMIT 10',
                    (ctx.guild.id,)
                ) as cursor:
                    top_starred = await cursor.fetchall()
            
            if not top_starred:
                embed = EmbedCreator.create_info_embed(
                    "No Starred Messages",
                    "This server has no starred messages yet."
                )
                await ctx.send(embed=embed)
                return
            
            embed = discord.Embed(
                title="⭐ Top Starred Messages",
                color=0xFFD700,
                timestamp=datetime.utcnow()
            )
            
            for i, (message_id, star_count) in enumerate(top_starred, 1):
                embed.add_field(
                    name=f"{i}. Message {message_id}",
                    value=f"⭐ {star_count} stars",
                    inline=True
                )
            
            embed.set_footer(text="ModCore • Starboard Leaderboard")
            await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(Starboard(bot))
