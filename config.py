import os
from dotenv import load_dotenv

load_dotenv()

TOKEN = os.getenv('DISCORD_TOKEN')
DEFAULT_PREFIX = '?'
EMBED_COLOR = 0x7289DA
SUCCESS_COLOR = 0x00FF00
ERROR_COLOR = 0xFF0000
WARNING_COLOR = 0xFFFF00
INFO_COLOR = 0x00FFFF

DATABASE_URL = 'modcore.db'

AUTOMOD_SETTINGS = {
    'max_mentions': 5,
    'max_duplicates': 3,
    'max_newlines': 10,
    'max_caps_percentage': 70,
    'spam_threshold': 5,
    'raid_threshold': 10
}

PUNISHMENT_TYPES = {
    'warn': 'Warning',
    'mute': 'Mute',
    'kick': 'Kick',
    'ban': 'Ban',
    'delete': 'Delete Message'
}

EMBED_ICONS = {
    'success': '✅',
    'error': '❌',
    'warning': '⚠️',
    'info': 'ℹ️',
    'moderation': '🔨',
    'automod': '🛡️',
    'giveaway': '🎉',
    'starboard': '⭐',
    'welcome': '👋',
    'goodbye': '👋',
    'role': '🎭',
    'utility': '🔧'
}
