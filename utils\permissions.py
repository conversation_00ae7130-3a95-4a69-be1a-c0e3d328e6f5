import discord
from discord.ext import commands
from typing import Union

class PermissionChecker:
    @staticmethod
    def has_permissions(member: discord.Member, **perms) -> bool:
        return member.guild_permissions >= discord.Permissions(**perms)
    
    @staticmethod
    def is_moderator(member: discord.Member) -> bool:
        return (
            member.guild_permissions.manage_messages or
            member.guild_permissions.kick_members or
            member.guild_permissions.ban_members or
            member.guild_permissions.manage_roles or
            member.guild_permissions.administrator
        )
    
    @staticmethod
    def is_admin(member: discord.Member) -> bool:
        return (
            member.guild_permissions.administrator or
            member.guild_permissions.manage_guild
        )
    
    @staticmethod
    def can_moderate(moderator: discord.Member, target: discord.Member) -> bool:
        if moderator.guild_permissions.administrator:
            return True
        
        if moderator == target:
            return False
        
        if target.guild_permissions.administrator:
            return False
        
        return moderator.top_role > target.top_role
    
    @staticmethod
    def bot_has_permissions(bot_member: discord.Member, **perms) -> bool:
        return bot_member.guild_permissions >= discord.Permissions(**perms)

def is_moderator():
    def predicate(ctx):
        return PermissionChecker.is_moderator(ctx.author)
    return commands.check(predicate)

def is_admin():
    def predicate(ctx):
        return PermissionChecker.is_admin(ctx.author)
    return commands.check(predicate)

def can_moderate_target():
    def predicate(ctx):
        if not hasattr(ctx, 'target_member'):
            return True
        return PermissionChecker.can_moderate(ctx.author, ctx.target_member)
    return commands.check(predicate)

def bot_has_perms(**perms):
    def predicate(ctx):
        return PermissionChecker.bot_has_permissions(ctx.guild.me, **perms)
    return commands.check(predicate)

async def check_hierarchy(ctx, target: discord.Member) -> bool:
    if not PermissionChecker.can_moderate(ctx.author, target):
        await ctx.send(embed=discord.Embed(
            title="❌ Permission Denied",
            description="You cannot moderate this user due to role hierarchy.",
            color=0xFF0000
        ))
        return False
    
    if not PermissionChecker.can_moderate(ctx.guild.me, target):
        await ctx.send(embed=discord.Embed(
            title="❌ Permission Denied",
            description="I cannot moderate this user due to role hierarchy.",
            color=0xFF0000
        ))
        return False
    
    return True

async def check_bot_permissions(ctx, **perms) -> bool:
    if not PermissionChecker.bot_has_permissions(ctx.guild.me, **perms):
        missing_perms = []
        for perm, value in perms.items():
            if value and not getattr(ctx.guild.me.guild_permissions, perm):
                missing_perms.append(perm.replace('_', ' ').title())
        
        await ctx.send(embed=discord.Embed(
            title="❌ Missing Permissions",
            description=f"I need the following permissions: {', '.join(missing_perms)}",
            color=0xFF0000
        ))
        return False
    
    return True
