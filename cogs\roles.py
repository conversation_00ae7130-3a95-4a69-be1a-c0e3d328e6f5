import discord
from discord.ext import commands
from typing import Union
import asyncio

from utils.embeds import Embed<PERSON><PERSON>
from utils.permissions import is_moderator, check_bot_permissions

class Roles(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
    
    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        if payload.user_id == self.bot.user.id:
            return
        
        role_id = await self.db.get_reaction_role(
            payload.guild_id, payload.message_id, str(payload.emoji)
        )
        
        if role_id:
            guild = self.bot.get_guild(payload.guild_id)
            member = guild.get_member(payload.user_id)
            role = guild.get_role(role_id)
            
            if member and role and role not in member.roles:
                try:
                    await member.add_roles(role, reason="Reaction role")
                except:
                    pass
    
    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        if payload.user_id == self.bot.user.id:
            return
        
        role_id = await self.db.get_reaction_role(
            payload.guild_id, payload.message_id, str(payload.emoji)
        )
        
        if role_id:
            guild = self.bot.get_guild(payload.guild_id)
            member = guild.get_member(payload.user_id)
            role = guild.get_role(role_id)
            
            if member and role and role in member.roles:
                try:
                    await member.remove_roles(role, reason="Reaction role removed")
                except:
                    pass
    
    @commands.command(name='addrole')
    @is_moderator()
    async def add_role(self, ctx, member: discord.Member, *, role: discord.Role):
        if not await check_bot_permissions(ctx, manage_roles=True):
            return
        
        if role >= ctx.guild.me.top_role:
            embed = EmbedCreator.create_error_embed(
                "Role Too High",
                "I cannot assign roles higher than or equal to my highest role."
            )
            await ctx.send(embed=embed)
            return
        
        if role in member.roles:
            embed = EmbedCreator.create_warning_embed(
                "Role Already Assigned",
                f"{member.mention} already has the {role.mention} role."
            )
            await ctx.send(embed=embed)
            return
        
        try:
            await member.add_roles(role, reason=f"Added by {ctx.author}")
            
            embed = EmbedCreator.create_success_embed(
                "Role Added",
                f"Successfully added {role.mention} to {member.mention}."
            )
            embed.set_thumbnail(url=member.display_avatar.url)
            await ctx.send(embed=embed)
        
        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I don't have permission to manage this role."
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='removerole')
    @is_moderator()
    async def remove_role(self, ctx, member: discord.Member, *, role: discord.Role):
        if not await check_bot_permissions(ctx, manage_roles=True):
            return
        
        if role >= ctx.guild.me.top_role:
            embed = EmbedCreator.create_error_embed(
                "Role Too High",
                "I cannot manage roles higher than or equal to my highest role."
            )
            await ctx.send(embed=embed)
            return
        
        if role not in member.roles:
            embed = EmbedCreator.create_warning_embed(
                "Role Not Found",
                f"{member.mention} doesn't have the {role.mention} role."
            )
            await ctx.send(embed=embed)
            return
        
        try:
            await member.remove_roles(role, reason=f"Removed by {ctx.author}")
            
            embed = EmbedCreator.create_success_embed(
                "Role Removed",
                f"Successfully removed {role.mention} from {member.mention}."
            )
            embed.set_thumbnail(url=member.display_avatar.url)
            await ctx.send(embed=embed)
        
        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I don't have permission to manage this role."
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='createrole')
    @is_moderator()
    async def create_role(self, ctx, *, name: str):
        if not await check_bot_permissions(ctx, manage_roles=True):
            return
        
        try:
            role = await ctx.guild.create_role(name=name, reason=f"Created by {ctx.author}")
            
            embed = EmbedCreator.create_success_embed(
                "Role Created",
                f"Successfully created role {role.mention}.\n**ID:** `{role.id}`"
            )
            await ctx.send(embed=embed)
        
        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I don't have permission to create roles."
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='deleterole')
    @is_moderator()
    async def delete_role(self, ctx, *, role: discord.Role):
        if not await check_bot_permissions(ctx, manage_roles=True):
            return
        
        if role >= ctx.guild.me.top_role:
            embed = EmbedCreator.create_error_embed(
                "Role Too High",
                "I cannot delete roles higher than or equal to my highest role."
            )
            await ctx.send(embed=embed)
            return
        
        if role == ctx.guild.default_role:
            embed = EmbedCreator.create_error_embed(
                "Cannot Delete",
                "I cannot delete the @everyone role."
            )
            await ctx.send(embed=embed)
            return
        
        role_name = role.name
        
        try:
            await role.delete(reason=f"Deleted by {ctx.author}")
            
            embed = EmbedCreator.create_success_embed(
                "Role Deleted",
                f"Successfully deleted role **{role_name}**."
            )
            await ctx.send(embed=embed)
        
        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I don't have permission to delete this role."
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='roleinfo')
    async def role_info(self, ctx, *, role: discord.Role):
        embed = discord.Embed(
            title=f"🎭 Role Information",
            description=f"**{role.name}**",
            color=role.color if role.color != discord.Color.default() else 0x7289DA,
            timestamp=ctx.message.created_at
        )
        
        embed.add_field(name="🆔 Role ID", value=f"`{role.id}`", inline=True)
        embed.add_field(name="📅 Created", value=f"<t:{int(role.created_at.timestamp())}:F>", inline=True)
        embed.add_field(name="👥 Members", value=str(len(role.members)), inline=True)
        
        embed.add_field(name="🎨 Color", value=str(role.color), inline=True)
        embed.add_field(name="📍 Position", value=str(role.position), inline=True)
        embed.add_field(name="🔗 Mentionable", value="Yes" if role.mentionable else "No", inline=True)
        
        embed.add_field(name="🔒 Hoisted", value="Yes" if role.hoist else "No", inline=True)
        embed.add_field(name="🤖 Managed", value="Yes" if role.managed else "No", inline=True)
        embed.add_field(name="🎯 Mention", value=role.mention, inline=True)
        
        permissions = [perm.replace('_', ' ').title() for perm, value in role.permissions if value]
        if permissions:
            embed.add_field(
                name="🔑 Key Permissions",
                value=', '.join(permissions[:10]) + ('...' if len(permissions) > 10 else ''),
                inline=False
            )
        
        embed.set_footer(text="ModCore • Role Information")
        await ctx.send(embed=embed)
    
    @commands.command(name='roles')
    async def list_roles(self, ctx):
        roles = sorted(ctx.guild.roles[1:], key=lambda r: r.position, reverse=True)
        
        embed = discord.Embed(
            title=f"🎭 Roles in {ctx.guild.name}",
            color=0x7289DA,
            timestamp=ctx.message.created_at
        )
        
        role_list = []
        for role in roles[:20]:
            member_count = len(role.members)
            role_list.append(f"{role.mention} - {member_count} members")
        
        embed.description = '\n'.join(role_list)
        
        if len(roles) > 20:
            embed.set_footer(text=f"Showing 20 of {len(roles)} roles")
        else:
            embed.set_footer(text=f"Total: {len(roles)} roles")
        
        await ctx.send(embed=embed)
    
    @commands.command(name='reactionrole', aliases=['rr'])
    @is_moderator()
    async def reaction_role(self, ctx, message_id: int, emoji: str, *, role: discord.Role):
        if not await check_bot_permissions(ctx, manage_roles=True, add_reactions=True):
            return
        
        if role >= ctx.guild.me.top_role:
            embed = EmbedCreator.create_error_embed(
                "Role Too High",
                "I cannot assign roles higher than or equal to my highest role."
            )
            await ctx.send(embed=embed)
            return
        
        try:
            message = await ctx.channel.fetch_message(message_id)
        except discord.NotFound:
            embed = EmbedCreator.create_error_embed(
                "Message Not Found",
                "I couldn't find a message with that ID in this channel."
            )
            await ctx.send(embed=embed)
            return
        
        try:
            await message.add_reaction(emoji)
        except discord.HTTPException:
            embed = EmbedCreator.create_error_embed(
                "Invalid Emoji",
                "I couldn't add that emoji to the message. Make sure it's a valid emoji."
            )
            await ctx.send(embed=embed)
            return
        
        await self.db.add_reaction_role(ctx.guild.id, message_id, emoji, role.id)
        
        embed = EmbedCreator.create_success_embed(
            "Reaction Role Created",
            f"Successfully set up reaction role!\n\n"
            f"**Message:** [Jump to message]({message.jump_url})\n"
            f"**Emoji:** {emoji}\n"
            f"**Role:** {role.mention}"
        )
        await ctx.send(embed=embed)
    
    @commands.command(name='removereactionrole', aliases=['rrremove'])
    @is_moderator()
    async def remove_reaction_role(self, ctx, message_id: int, emoji: str):
        await self.db.remove_reaction_role(ctx.guild.id, message_id, emoji)
        
        embed = EmbedCreator.create_success_embed(
            "Reaction Role Removed",
            f"Successfully removed reaction role for {emoji} on message `{message_id}`."
        )
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(Roles(bot))
