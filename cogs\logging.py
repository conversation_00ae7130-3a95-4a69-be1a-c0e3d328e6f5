import discord
from discord.ext import commands
from datetime import datetime

from utils.embeds import EmbedCreator
from utils.permissions import is_moderator

class Logging(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
    
    @commands.command(name='setlogchannel', aliases=['logchannel'])
    @is_moderator()
    async def set_log_channel(self, ctx, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        
        await self.db.update_guild_setting(ctx.guild.id, 'mod_log_channel', channel.id)
        
        embed = EmbedCreator.create_success_embed(
            "Log Channel Set",
            f"Moderation logs will now be sent to {channel.mention}."
        )
        await ctx.send(embed=embed)
        
        test_embed = discord.Embed(
            title="📋 Logging Test",
            description="This is a test message to confirm logging is working properly.",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        test_embed.set_footer(text="ModCore • Logging System")
        
        await channel.send(embed=test_embed)
    
    @commands.command(name='removelogchannel', aliases=['unsetlog'])
    @is_moderator()
    async def remove_log_channel(self, ctx):
        await self.db.update_guild_setting(ctx.guild.id, 'mod_log_channel', None)
        
        embed = EmbedCreator.create_success_embed(
            "Log Channel Removed",
            "Moderation logging has been disabled."
        )
        await ctx.send(embed=embed)
    
    @commands.Cog.listener()
    async def on_message_delete(self, message):
        if message.author.bot or not message.guild:
            return
        
        settings = await self.db.get_guild_settings(message.guild.id)
        log_channel_id = settings.get('mod_log_channel')
        
        if not log_channel_id:
            return
        
        log_channel = message.guild.get_channel(log_channel_id)
        if not log_channel:
            return
        
        embed = discord.Embed(
            title="🗑️ Message Deleted",
            color=0xFF6B6B,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 Author", value=f"{message.author.mention}\n`{message.author.id}`", inline=True)
        embed.add_field(name="📍 Channel", value=f"{message.channel.mention}\n`{message.channel.id}`", inline=True)
        embed.add_field(name="🕒 Sent", value=f"<t:{int(message.created_at.timestamp())}:R>", inline=True)
        
        if message.content:
            content = message.content[:1024] if len(message.content) <= 1024 else message.content[:1021] + "..."
            embed.add_field(name="📝 Content", value=f"```{content}```", inline=False)
        
        if message.attachments:
            attachment_list = []
            for attachment in message.attachments[:5]:
                attachment_list.append(f"[{attachment.filename}]({attachment.url})")
            embed.add_field(name="📎 Attachments", value='\n'.join(attachment_list), inline=False)
        
        embed.set_thumbnail(url=message.author.display_avatar.url)
        embed.set_footer(text="ModCore • Message Logging")
        
        try:
            await log_channel.send(embed=embed)
        except:
            pass
    
    @commands.Cog.listener()
    async def on_message_edit(self, before, after):
        if before.author.bot or not before.guild or before.content == after.content:
            return
        
        settings = await self.db.get_guild_settings(before.guild.id)
        log_channel_id = settings.get('mod_log_channel')
        
        if not log_channel_id:
            return
        
        log_channel = before.guild.get_channel(log_channel_id)
        if not log_channel:
            return
        
        embed = discord.Embed(
            title="✏️ Message Edited",
            color=0xFFD700,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 Author", value=f"{before.author.mention}\n`{before.author.id}`", inline=True)
        embed.add_field(name="📍 Channel", value=f"{before.channel.mention}\n`{before.channel.id}`", inline=True)
        embed.add_field(name="🔗 Jump", value=f"[Go to message]({after.jump_url})", inline=True)
        
        if before.content:
            before_content = before.content[:512] if len(before.content) <= 512 else before.content[:509] + "..."
            embed.add_field(name="📝 Before", value=f"```{before_content}```", inline=False)
        
        if after.content:
            after_content = after.content[:512] if len(after.content) <= 512 else after.content[:509] + "..."
            embed.add_field(name="📝 After", value=f"```{after_content}```", inline=False)
        
        embed.set_thumbnail(url=before.author.display_avatar.url)
        embed.set_footer(text="ModCore • Message Logging")
        
        try:
            await log_channel.send(embed=embed)
        except:
            pass
    
    @commands.Cog.listener()
    async def on_member_join(self, member):
        settings = await self.db.get_guild_settings(member.guild.id)
        log_channel_id = settings.get('mod_log_channel')
        
        if not log_channel_id:
            return
        
        log_channel = member.guild.get_channel(log_channel_id)
        if not log_channel:
            return
        
        embed = discord.Embed(
            title="📥 Member Joined",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 User", value=f"{member.mention}\n`{member.id}`", inline=True)
        embed.add_field(name="📊 Member Count", value=f"{member.guild.member_count}", inline=True)
        embed.add_field(name="📅 Account Created", value=f"<t:{int(member.created_at.timestamp())}:R>", inline=True)
        
        account_age = datetime.utcnow() - member.created_at
        if account_age.days < 7:
            embed.add_field(name="⚠️ Warning", value="New account (less than 7 days old)", inline=False)
        
        embed.set_thumbnail(url=member.display_avatar.url)
        embed.set_footer(text="ModCore • Member Logging")
        
        try:
            await log_channel.send(embed=embed)
        except:
            pass
    
    @commands.Cog.listener()
    async def on_member_remove(self, member):
        settings = await self.db.get_guild_settings(member.guild.id)
        log_channel_id = settings.get('mod_log_channel')
        
        if not log_channel_id:
            return
        
        log_channel = member.guild.get_channel(log_channel_id)
        if not log_channel:
            return
        
        embed = discord.Embed(
            title="📤 Member Left",
            color=0xFF6B6B,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 User", value=f"{member.display_name}\n`{member.id}`", inline=True)
        embed.add_field(name="📊 Member Count", value=f"{member.guild.member_count}", inline=True)
        embed.add_field(name="⏰ Joined", value=f"<t:{int(member.joined_at.timestamp())}:R>" if member.joined_at else "Unknown", inline=True)
        
        if member.roles[1:]:
            roles = [role.mention for role in member.roles[1:][:10]]
            embed.add_field(name="🎭 Roles", value=', '.join(roles), inline=False)
        
        embed.set_thumbnail(url=member.display_avatar.url)
        embed.set_footer(text="ModCore • Member Logging")
        
        try:
            await log_channel.send(embed=embed)
        except:
            pass
    
    @commands.Cog.listener()
    async def on_member_ban(self, guild, user):
        settings = await self.db.get_guild_settings(guild.id)
        log_channel_id = settings.get('mod_log_channel')
        
        if not log_channel_id:
            return
        
        log_channel = guild.get_channel(log_channel_id)
        if not log_channel:
            return
        
        embed = discord.Embed(
            title="🔨 Member Banned",
            color=0xFF0000,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 User", value=f"{user.mention}\n`{user.id}`", inline=True)
        embed.add_field(name="📅 Account Created", value=f"<t:{int(user.created_at.timestamp())}:R>", inline=True)
        
        try:
            ban_entry = await guild.fetch_ban(user)
            if ban_entry.reason:
                embed.add_field(name="📝 Reason", value=ban_entry.reason, inline=False)
        except:
            pass
        
        embed.set_thumbnail(url=user.display_avatar.url)
        embed.set_footer(text="ModCore • Member Logging")
        
        try:
            await log_channel.send(embed=embed)
        except:
            pass
    
    @commands.Cog.listener()
    async def on_member_unban(self, guild, user):
        settings = await self.db.get_guild_settings(guild.id)
        log_channel_id = settings.get('mod_log_channel')
        
        if not log_channel_id:
            return
        
        log_channel = guild.get_channel(log_channel_id)
        if not log_channel:
            return
        
        embed = discord.Embed(
            title="🔓 Member Unbanned",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="👤 User", value=f"{user.mention}\n`{user.id}`", inline=True)
        embed.add_field(name="📅 Account Created", value=f"<t:{int(user.created_at.timestamp())}:R>", inline=True)
        
        embed.set_thumbnail(url=user.display_avatar.url)
        embed.set_footer(text="ModCore • Member Logging")
        
        try:
            await log_channel.send(embed=embed)
        except:
            pass

async def setup(bot):
    await bot.add_cog(Logging(bot))
