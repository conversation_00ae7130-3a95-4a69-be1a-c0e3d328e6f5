# ModCore - Discord Moderation Bot

A comprehensive Discord moderation bot that replicates and enhances Dyno's functionality with modern features and beautiful embeds.

## Features

### 🔨 Moderation
- **User Management**: Ban, kick, mute, warn, softban
- **Message Management**: Purge messages with advanced filters
- **Channel Control**: Lock/unlock channels, server lockdown
- **Warning System**: Track and manage user warnings

### 🛡️ Auto-Moderation
- **Spam Detection**: Automatic spam message detection
- **Word Filtering**: Customizable banned word lists
- **Link Filtering**: Whitelist/blacklist URL domains
- **Caps Filter**: Limit excessive capital letters
- **Mention Spam**: Prevent mass mention abuse
- **Duplicate Detection**: Block repeated messages

### 🎭 Role Management
- **Role Commands**: Add, remove, create, delete roles
- **Reaction Roles**: Interactive role assignment
- **Role Information**: Detailed role statistics
- **Permission Checks**: Hierarchy-based role management

### 🎉 Giveaways
- **Create Giveaways**: Timed giveaway system
- **Multiple Winners**: Support for multiple winners
- **Reroll System**: Reroll giveaway winners
- **Auto-End**: Automatic giveaway completion

### 👋 Welcome System
- **Welcome Messages**: Customizable join messages
- **Goodbye Messages**: Customizable leave messages
- **Variable Support**: Dynamic message variables
- **Channel Configuration**: Dedicated welcome channels

### ⭐ Starboard
- **Star Messages**: Highlight popular messages
- **Configurable Threshold**: Set star requirements
- **Leaderboard**: Track top starred messages
- **Auto-Update**: Real-time star count updates

### 📝 Custom Commands
- **User Commands**: Create custom server commands
- **Variable System**: Dynamic command responses
- **Command Management**: Edit, delete, list commands
- **Testing Tools**: Test commands before deployment

### 📋 Logging
- **Message Logs**: Track message edits/deletions
- **Member Logs**: Monitor joins/leaves/bans
- **Moderation Logs**: Log all moderation actions
- **Configurable**: Set custom log channels

### 🔧 Utility
- **Server Info**: Detailed server statistics
- **User Info**: Comprehensive user profiles
- **Avatar Display**: Show user avatars
- **Polls**: Create interactive polls
- **Reminders**: Set personal reminders
- **Games**: Coin flip, dice roll
- **Ping**: Bot latency testing
- **Uptime**: Bot status information

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ModCore
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env and add your Discord bot token
   ```

4. **Run the bot**
   ```bash
   python main.py
   ```

## Configuration

### Discord Bot Setup
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to "Bot" section and create a bot
4. Copy the bot token to your `.env` file
5. Enable necessary intents (Message Content Intent, Server Members Intent)

### Bot Permissions
The bot requires the following permissions:
- Manage Messages
- Manage Roles
- Manage Channels
- Ban Members
- Kick Members
- Moderate Members (for timeouts)
- Add Reactions
- Read Message History
- Send Messages
- Embed Links
- Attach Files

### Invite Link
Generate an invite link with the required permissions:
```
https://discord.com/api/oauth2/authorize?client_id=YOUR_BOT_ID&permissions=1099511627775&scope=bot
```

## Commands

### Moderation Commands
- `?ban <user> [reason]` - Ban a user
- `?kick <user> [reason]` - Kick a user
- `?mute <user> [duration] [reason]` - Mute a user
- `?warn <user> [reason]` - Warn a user
- `?purge [amount]` - Delete messages
- `?lock [channel]` - Lock a channel
- `?lockdown` - Lock all channels

### AutoMod Commands
- `?automod` - View automod settings
- `?automod spam [on/off]` - Toggle spam detection
- `?automod links [on/off]` - Toggle link filtering
- `?automod banword <words>` - Ban words
- `?automod whitelist <domains>` - Whitelist domains

### Role Commands
- `?addrole <user> <role>` - Add role to user
- `?removerole <user> <role>` - Remove role from user
- `?reactionrole <message_id> <emoji> <role>` - Create reaction role
- `?roleinfo <role>` - Show role information

### Giveaway Commands
- `?giveaway <duration> <winners> <prize>` - Start giveaway
- `?gend <message_id>` - End giveaway
- `?greroll <message_id>` - Reroll giveaway

### Welcome Commands
- `?welcome channel <channel>` - Set welcome channel
- `?welcome message <message>` - Set welcome message
- `?welcome test` - Test welcome system

### Starboard Commands
- `?starboard channel <channel>` - Set starboard channel
- `?starboard threshold <number>` - Set star threshold
- `?star <message_id>` - Preview starred message

### Custom Commands
- `?addcommand <name> <response>` - Create custom command
- `?removecommand <name>` - Delete custom command
- `?commands` - List custom commands

### Utility Commands
- `?help [command]` - Show help information
- `?serverinfo` - Show server information
- `?userinfo [user]` - Show user information
- `?avatar [user]` - Show user avatar
- `?poll <question> <options>` - Create a poll
- `?remind <time> <reminder>` - Set a reminder
- `?ping` - Check bot latency

## Database

The bot uses SQLite for data storage with the following tables:
- `guild_settings` - Server configuration
- `automod_settings` - AutoMod configuration
- `warnings` - User warnings
- `reaction_roles` - Reaction role mappings
- `giveaways` - Active giveaways
- `custom_commands` - Server custom commands
- `starboard_messages` - Starred messages

## Support

For support, feature requests, or bug reports:
- Create an issue on GitHub
- Join our Discord server: [discord.gg/modcore](https://discord.gg/modcore)
- Email: <EMAIL>

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Acknowledgments

- Inspired by Dyno Discord Bot
- Built with discord.py
- Uses SQLite for data persistence
- Designed for modern Discord servers
