import discord
from discord.ext import commands
import re
import asyncio
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List

from utils.embeds import EmbedCreator
from utils.permissions import is_moderator
from config import AUTOMOD_SETTINGS

class AutoMod(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        self.spam_tracker = defaultdict(lambda: deque(maxlen=5))
        self.duplicate_tracker = defaultdict(lambda: deque(maxlen=3))

    @commands.Cog.listener()
    async def on_message(self, message):
        if message.author.bot or not message.guild:
            return

        if message.author.guild_permissions.manage_messages:
            return

        settings = await self.db.get_guild_settings(message.guild.id)
        if not settings.get('automod_enabled'):
            return

        automod_settings = await self.db.get_automod_settings(message.guild.id)

        violations = []

        if automod_settings['spam_enabled']:
            if await self.check_spam(message):
                violations.append("Spam")

        if automod_settings['caps_enabled']:
            if await self.check_caps(message):
                violations.append("Excessive Caps")

        if automod_settings['mentions_enabled']:
            if await self.check_mentions(message):
                violations.append("Mention Spam")

        if automod_settings['duplicates_enabled']:
            if await self.check_duplicates(message):
                violations.append("Duplicate Messages")

        if automod_settings['banned_words']:
            if await self.check_banned_words(message, automod_settings['banned_words']):
                violations.append("Banned Words")

        if automod_settings['links_enabled']:
            if await self.check_links(message, automod_settings.get('whitelisted_links', '')):
                violations.append("Unauthorized Links")

        if violations:
            await self.handle_violation(message, violations, automod_settings['punishment_type'])

    async def check_spam(self, message) -> bool:
        user_id = message.author.id
        now = datetime.utcnow()

        self.spam_tracker[user_id].append(now)

        if len(self.spam_tracker[user_id]) >= AUTOMOD_SETTINGS['spam_threshold']:
            time_diff = now - self.spam_tracker[user_id][0]
            if time_diff.total_seconds() <= 5:
                return True

        return False

    async def check_caps(self, message) -> bool:
        if len(message.content) < 10:
            return False

        caps_count = sum(1 for c in message.content if c.isupper())
        caps_percentage = (caps_count / len(message.content)) * 100

        return caps_percentage > AUTOMOD_SETTINGS['max_caps_percentage']

    async def check_mentions(self, message) -> bool:
        mention_count = len(message.mentions) + len(message.role_mentions)
        return mention_count > AUTOMOD_SETTINGS['max_mentions']

    async def check_duplicates(self, message) -> bool:
        user_id = message.author.id
        content = message.content.lower().strip()

        if len(content) < 5:
            return False

        self.duplicate_tracker[user_id].append(content)

        if len(self.duplicate_tracker[user_id]) >= AUTOMOD_SETTINGS['max_duplicates']:
            if all(msg == content for msg in self.duplicate_tracker[user_id]):
                return True

        return False

    async def check_banned_words(self, message, banned_words: str) -> bool:
        if not banned_words:
            return False

        words = [word.strip().lower() for word in banned_words.split(',')]
        content = message.content.lower()

        for word in words:
            if word in content:
                return True

        return False

    async def check_links(self, message, whitelisted_links: str) -> bool:
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        urls = re.findall(url_pattern, message.content)

        if not urls:
            return False

        if not whitelisted_links:
            return True

        whitelist = [domain.strip().lower() for domain in whitelisted_links.split(',')]

        for url in urls:
            domain = re.search(r'://([^/]+)', url)
            if domain:
                domain = domain.group(1).lower()
                if not any(allowed in domain for allowed in whitelist):
                    return True

        return False

    async def handle_violation(self, message, violations: List[str], punishment_type: str):
        try:
            await message.delete()
        except:
            pass

        violation_text = ", ".join(violations)

        embed = EmbedCreator.create_automod_embed(
            violation_text, message.author, punishment_type.title(), message.content[:100]
        )

        log_msg = await message.channel.send(embed=embed, delete_after=10)

        settings = await self.db.get_guild_settings(message.guild.id)
        if settings.get('mod_log_channel'):
            log_channel = message.guild.get_channel(settings['mod_log_channel'])
            if log_channel:
                await log_channel.send(embed=embed)

        if punishment_type == 'warn':
            await self.db.add_warning(message.guild.id, message.author.id, self.bot.user.id, f"AutoMod: {violation_text}")

        elif punishment_type == 'mute':
            try:
                await message.author.timeout(timedelta(minutes=10), reason=f"AutoMod: {violation_text}")
            except:
                pass

        elif punishment_type == 'kick':
            try:
                await message.author.kick(reason=f"AutoMod: {violation_text}")
            except:
                pass

        elif punishment_type == 'ban':
            try:
                await message.author.ban(reason=f"AutoMod: {violation_text}")
            except:
                pass

    @commands.group(name='automod', invoke_without_command=True)
    @is_moderator()
    async def automod(self, ctx):
        settings = await self.db.get_automod_settings(ctx.guild.id)

        embed = discord.Embed(
            title="🛡️ AutoMod Settings",
            color=0x7289DA,
            timestamp=datetime.utcnow()
        )

        embed.add_field(
            name="📊 Status",
            value="🟢 Enabled" if settings['spam_enabled'] else "🔴 Disabled",
            inline=True
        )

        embed.add_field(
            name="⚡ Spam Detection",
            value="🟢 On" if settings['spam_enabled'] else "🔴 Off",
            inline=True
        )

        embed.add_field(
            name="🔗 Link Filter",
            value="🟢 On" if settings['links_enabled'] else "🔴 Off",
            inline=True
        )

        embed.add_field(
            name="🔠 Caps Filter",
            value="🟢 On" if settings['caps_enabled'] else "🔴 Off",
            inline=True
        )

        embed.add_field(
            name="📢 Mention Spam",
            value="🟢 On" if settings['mentions_enabled'] else "🔴 Off",
            inline=True
        )

        embed.add_field(
            name="📝 Duplicate Filter",
            value="🟢 On" if settings['duplicates_enabled'] else "🔴 Off",
            inline=True
        )

        embed.add_field(
            name="🔨 Punishment",
            value=settings['punishment_type'].title(),
            inline=True
        )

        banned_words_count = len([w for w in settings['banned_words'].split(',') if w.strip()]) if settings['banned_words'] else 0
        embed.add_field(
            name="🚫 Banned Words",
            value=f"{banned_words_count} words",
            inline=True
        )

        whitelist_count = len([w for w in settings.get('whitelisted_links', '').split(',') if w.strip()]) if settings.get('whitelisted_links') else 0
        embed.add_field(
            name="✅ Whitelisted Links",
            value=f"{whitelist_count} domains",
            inline=True
        )

        embed.set_footer(text="Use ?automod <setting> to configure")
        await ctx.send(embed=embed)

    @automod.command(name='spam')
    @is_moderator()
    async def toggle_spam(self, ctx, enabled: bool = None):
        if enabled is None:
            settings = await self.db.get_automod_settings(ctx.guild.id)
            enabled = not settings['spam_enabled']

        await self.db.update_automod_setting(ctx.guild.id, 'spam_enabled', enabled)

        embed = EmbedCreator.create_success_embed(
            "Spam Detection Updated",
            f"Spam detection is now {'enabled' if enabled else 'disabled'}."
        )
        await ctx.send(embed=embed)

    @automod.command(name='links')
    @is_moderator()
    async def toggle_links(self, ctx, enabled: bool = None):
        if enabled is None:
            settings = await self.db.get_automod_settings(ctx.guild.id)
            enabled = not settings['links_enabled']

        await self.db.update_automod_setting(ctx.guild.id, 'links_enabled', enabled)

        embed = EmbedCreator.create_success_embed(
            "Link Filter Updated",
            f"Link filtering is now {'enabled' if enabled else 'disabled'}."
        )
        await ctx.send(embed=embed)

    @automod.command(name='caps')
    @is_moderator()
    async def toggle_caps(self, ctx, enabled: bool = None):
        if enabled is None:
            settings = await self.db.get_automod_settings(ctx.guild.id)
            enabled = not settings['caps_enabled']

        await self.db.update_automod_setting(ctx.guild.id, 'caps_enabled', enabled)

        embed = EmbedCreator.create_success_embed(
            "Caps Filter Updated",
            f"Caps filtering is now {'enabled' if enabled else 'disabled'}."
        )
        await ctx.send(embed=embed)

    @automod.command(name='mentions')
    @is_moderator()
    async def toggle_mentions(self, ctx, enabled: bool = None):
        if enabled is None:
            settings = await self.db.get_automod_settings(ctx.guild.id)
            enabled = not settings['mentions_enabled']

        await self.db.update_automod_setting(ctx.guild.id, 'mentions_enabled', enabled)

        embed = EmbedCreator.create_success_embed(
            "Mention Spam Filter Updated",
            f"Mention spam filtering is now {'enabled' if enabled else 'disabled'}."
        )
        await ctx.send(embed=embed)

    @automod.command(name='duplicates')
    @is_moderator()
    async def toggle_duplicates(self, ctx, enabled: bool = None):
        if enabled is None:
            settings = await self.db.get_automod_settings(ctx.guild.id)
            enabled = not settings['duplicates_enabled']

        await self.db.update_automod_setting(ctx.guild.id, 'duplicates_enabled', enabled)

        embed = EmbedCreator.create_success_embed(
            "Duplicate Filter Updated",
            f"Duplicate message filtering is now {'enabled' if enabled else 'disabled'}."
        )
        await ctx.send(embed=embed)

    @automod.command(name='punishment')
    @is_moderator()
    async def set_punishment(self, ctx, punishment_type: str):
        valid_punishments = ['warn', 'mute', 'kick', 'ban', 'delete']

        if punishment_type.lower() not in valid_punishments:
            embed = EmbedCreator.create_error_embed(
                "Invalid Punishment",
                f"Valid punishments: {', '.join(valid_punishments)}"
            )
            await ctx.send(embed=embed)
            return

        await self.db.update_automod_setting(ctx.guild.id, 'punishment_type', punishment_type.lower())

        embed = EmbedCreator.create_success_embed(
            "Punishment Updated",
            f"AutoMod punishment set to: **{punishment_type.title()}**"
        )
        await ctx.send(embed=embed)

    @automod.command(name='banword', aliases=['addword'])
    @is_moderator()
    async def ban_word(self, ctx, *, words: str):
        settings = await self.db.get_automod_settings(ctx.guild.id)
        current_words = settings['banned_words'] if settings['banned_words'] else ''

        new_words = [word.strip().lower() for word in words.split(',')]
        existing_words = [word.strip().lower() for word in current_words.split(',') if word.strip()]

        all_words = list(set(existing_words + new_words))
        updated_words = ', '.join(all_words)

        await self.db.update_automod_setting(ctx.guild.id, 'banned_words', updated_words)

        embed = EmbedCreator.create_success_embed(
            "Words Banned",
            f"Added {len(new_words)} word(s) to the banned list.\n**Total banned words:** {len(all_words)}"
        )
        await ctx.send(embed=embed)

    @automod.command(name='unbanword', aliases=['removeword'])
    @is_moderator()
    async def unban_word(self, ctx, *, words: str):
        settings = await self.db.get_automod_settings(ctx.guild.id)
        current_words = settings['banned_words'] if settings['banned_words'] else ''

        remove_words = [word.strip().lower() for word in words.split(',')]
        existing_words = [word.strip().lower() for word in current_words.split(',') if word.strip()]

        updated_words = [word for word in existing_words if word not in remove_words]
        updated_words_str = ', '.join(updated_words)

        await self.db.update_automod_setting(ctx.guild.id, 'banned_words', updated_words_str)

        embed = EmbedCreator.create_success_embed(
            "Words Unbanned",
            f"Removed {len(remove_words)} word(s) from the banned list.\n**Total banned words:** {len(updated_words)}"
        )
        await ctx.send(embed=embed)

    @automod.command(name='whitelist')
    @is_moderator()
    async def whitelist_domain(self, ctx, *, domains: str):
        settings = await self.db.get_automod_settings(ctx.guild.id)
        current_domains = settings.get('whitelisted_links', '') if settings.get('whitelisted_links') else ''

        new_domains = [domain.strip().lower() for domain in domains.split(',')]
        existing_domains = [domain.strip().lower() for domain in current_domains.split(',') if domain.strip()]

        all_domains = list(set(existing_domains + new_domains))
        updated_domains = ', '.join(all_domains)

        await self.db.update_automod_setting(ctx.guild.id, 'whitelisted_links', updated_domains)

        embed = EmbedCreator.create_success_embed(
            "Domains Whitelisted",
            f"Added {len(new_domains)} domain(s) to the whitelist.\n**Total whitelisted domains:** {len(all_domains)}"
        )
        await ctx.send(embed=embed)

    @automod.command(name='unwhitelist')
    @is_moderator()
    async def unwhitelist_domain(self, ctx, *, domains: str):
        settings = await self.db.get_automod_settings(ctx.guild.id)
        current_domains = settings.get('whitelisted_links', '') if settings.get('whitelisted_links') else ''

        remove_domains = [domain.strip().lower() for domain in domains.split(',')]
        existing_domains = [domain.strip().lower() for domain in current_domains.split(',') if domain.strip()]

        updated_domains = [domain for domain in existing_domains if domain not in remove_domains]
        updated_domains_str = ', '.join(updated_domains)

        await self.db.update_automod_setting(ctx.guild.id, 'whitelisted_links', updated_domains_str)

        embed = EmbedCreator.create_success_embed(
            "Domains Removed",
            f"Removed {len(remove_domains)} domain(s) from the whitelist.\n**Total whitelisted domains:** {len(updated_domains)}"
        )
        await ctx.send(embed=embed)

    @automod.command(name='setup')
    @is_moderator()
    async def setup_automod(self, ctx):
        embed = discord.Embed(
            title="🛡️ AutoMod Quick Setup",
            description="React with the emojis below to configure AutoMod features:",
            color=0x7289DA,
            timestamp=datetime.utcnow()
        )

        embed.add_field(
            name="⚡ Spam Detection",
            value="Detects rapid message sending",
            inline=True
        )

        embed.add_field(
            name="🔗 Link Filter",
            value="Blocks unauthorized links",
            inline=True
        )

        embed.add_field(
            name="🔠 Caps Filter",
            value="Limits excessive capitals",
            inline=True
        )

        embed.add_field(
            name="📢 Mention Spam",
            value="Prevents mass mentions",
            inline=True
        )

        embed.add_field(
            name="📝 Duplicate Filter",
            value="Blocks repeated messages",
            inline=True
        )

        embed.add_field(
            name="🚫 Word Filter",
            value="Bans specific words",
            inline=True
        )

        embed.set_footer(text="Use individual commands for detailed configuration")

        msg = await ctx.send(embed=embed)

        reactions = ['⚡', '🔗', '🔠', '📢', '📝', '🚫']
        for reaction in reactions:
            await msg.add_reaction(reaction)

async def setup(bot):
    await bot.add_cog(AutoMod(bot))
