import discord
from discord.ext import commands
from datetime import datetime, timedelta
import asyncio
import random
import re

from utils.embeds import EmbedCreator
from utils.permissions import is_moderator

class Giveaways(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
    
    @commands.command(name='giveaway', aliases=['gstart'])
    @is_moderator()
    async def start_giveaway(self, ctx, duration: str, winners: int, *, prize: str):
        time_regex = re.match(r'(\d+)([smhd])', duration.lower())
        if not time_regex:
            embed = EmbedCreator.create_error_embed(
                "Invalid Duration",
                "Please use format: `1s`, `5m`, `2h`, `1d`"
            )
            await ctx.send(embed=embed)
            return
        
        amount, unit = time_regex.groups()
        amount = int(amount)
        
        if unit == 's':
            delta = timedelta(seconds=amount)
        elif unit == 'm':
            delta = timedelta(minutes=amount)
        elif unit == 'h':
            delta = timedelta(hours=amount)
        elif unit == 'd':
            delta = timedelta(days=amount)
        
        if delta > timedelta(days=30):
            embed = EmbedCreator.create_error_embed(
                "Duration Too Long",
                "Maximum giveaway duration is 30 days."
            )
            await ctx.send(embed=embed)
            return
        
        if winners < 1 or winners > 20:
            embed = EmbedCreator.create_error_embed(
                "Invalid Winner Count",
                "Winner count must be between 1 and 20."
            )
            await ctx.send(embed=embed)
            return
        
        end_time = datetime.utcnow() + delta
        end_timestamp = f"<t:{int(end_time.timestamp())}:R>"
        
        embed = EmbedCreator.create_giveaway_embed(
            prize, ctx.author, winners, end_timestamp
        )
        
        giveaway_msg = await ctx.send(embed=embed)
        await giveaway_msg.add_reaction('🎉')
        
        await self.db.add_giveaway(
            giveaway_msg.id, ctx.guild.id, ctx.channel.id,
            ctx.author.id, prize, winners, end_time.isoformat()
        )
        
        confirmation_embed = EmbedCreator.create_success_embed(
            "Giveaway Started",
            f"Giveaway created successfully!\n\n"
            f"**Prize:** {prize}\n"
            f"**Duration:** {duration}\n"
            f"**Winners:** {winners}\n"
            f"**Message ID:** `{giveaway_msg.id}`"
        )
        
        try:
            await ctx.message.delete()
        except:
            pass
        
        temp_msg = await ctx.send(embed=confirmation_embed)
        await asyncio.sleep(10)
        try:
            await temp_msg.delete()
        except:
            pass
    
    @commands.command(name='gend', aliases=['giveawayend'])
    @is_moderator()
    async def end_giveaway(self, ctx, message_id: int):
        try:
            message = await ctx.channel.fetch_message(message_id)
        except discord.NotFound:
            embed = EmbedCreator.create_error_embed(
                "Message Not Found",
                "I couldn't find a giveaway message with that ID in this channel."
            )
            await ctx.send(embed=embed)
            return
        
        if not message.embeds or "GIVEAWAY" not in message.embeds[0].title:
            embed = EmbedCreator.create_error_embed(
                "Invalid Message",
                "That message is not a giveaway."
            )
            await ctx.send(embed=embed)
            return
        
        reaction = discord.utils.get(message.reactions, emoji='🎉')
        if not reaction:
            embed = EmbedCreator.create_error_embed(
                "No Reactions",
                "This giveaway has no reactions."
            )
            await ctx.send(embed=embed)
            return
        
        users = [user async for user in reaction.users() if not user.bot]
        
        if not users:
            embed = EmbedCreator.create_warning_embed(
                "🎉 Giveaway Ended!",
                f"**Prize:** {message.embeds[0].description.split('**Prize:** ')[1].split('\\n')[0]}\n**Winners:** No valid entries"
            )
            await message.edit(embed=embed)
            await ctx.send("🎉 Giveaway ended with no valid entries!")
            return
        
        embed_desc = message.embeds[0].description
        prize = embed_desc.split('**Prize:** ')[1].split('\n')[0]
        
        winners_field = None
        for field in message.embeds[0].fields:
            if field.name == "🎯 Winners":
                winners_count = int(field.value)
                break
        else:
            winners_count = 1
        
        if len(users) < winners_count:
            winners = users
        else:
            winners = random.sample(users, winners_count)
        
        winner_mentions = ', '.join([user.mention for user in winners])
        
        embed = EmbedCreator.create_success_embed(
            "🎉 Giveaway Ended!",
            f"**Prize:** {prize}\n**Winners:** {winner_mentions}"
        )
        
        await message.edit(embed=embed)
        
        announcement_embed = discord.Embed(
            title="🎉 Giveaway Results",
            description=f"**Prize:** {prize}\n**Winners:** {winner_mentions}",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        announcement_embed.add_field(
            name="📍 Original Message",
            value=f"[Jump to giveaway]({message.jump_url})",
            inline=False
        )
        announcement_embed.set_footer(text="Congratulations to the winners!")
        
        await ctx.send(f"🎉 **Giveaway Ended!** {winner_mentions}", embed=announcement_embed)
        
        await self.db.end_giveaway(message_id)
    
    @commands.command(name='greroll', aliases=['giveawayreroll'])
    @is_moderator()
    async def reroll_giveaway(self, ctx, message_id: int):
        try:
            message = await ctx.channel.fetch_message(message_id)
        except discord.NotFound:
            embed = EmbedCreator.create_error_embed(
                "Message Not Found",
                "I couldn't find a message with that ID in this channel."
            )
            await ctx.send(embed=embed)
            return
        
        if not message.embeds or "Giveaway Ended" not in message.embeds[0].title:
            embed = EmbedCreator.create_error_embed(
                "Invalid Message",
                "That message is not an ended giveaway."
            )
            await ctx.send(embed=embed)
            return
        
        reaction = discord.utils.get(message.reactions, emoji='🎉')
        if not reaction:
            embed = EmbedCreator.create_error_embed(
                "No Reactions",
                "This giveaway has no reactions to reroll from."
            )
            await ctx.send(embed=embed)
            return
        
        users = [user async for user in reaction.users() if not user.bot]
        
        if not users:
            embed = EmbedCreator.create_warning_embed(
                "No Valid Entries",
                "There are no valid entries to reroll from."
            )
            await ctx.send(embed=embed)
            return
        
        embed_desc = message.embeds[0].description
        prize = embed_desc.split('**Prize:** ')[1].split('\n')[0]
        
        original_winners = embed_desc.split('**Winners:** ')[1]
        original_winner_count = len(original_winners.split(', '))
        
        if len(users) < original_winner_count:
            new_winners = users
        else:
            new_winners = random.sample(users, original_winner_count)
        
        new_winner_mentions = ', '.join([user.mention for user in new_winners])
        
        embed = EmbedCreator.create_success_embed(
            "🎉 Giveaway Ended! (Rerolled)",
            f"**Prize:** {prize}\n**Winners:** {new_winner_mentions}"
        )
        
        await message.edit(embed=embed)
        
        reroll_embed = discord.Embed(
            title="🎲 Giveaway Rerolled",
            description=f"**Prize:** {prize}\n**New Winners:** {new_winner_mentions}",
            color=0xFFD700,
            timestamp=datetime.utcnow()
        )
        reroll_embed.add_field(
            name="📍 Original Message",
            value=f"[Jump to giveaway]({message.jump_url})",
            inline=False
        )
        reroll_embed.set_footer(text="Giveaway has been rerolled!")
        
        await ctx.send(f"🎲 **Giveaway Rerolled!** {new_winner_mentions}", embed=reroll_embed)
    
    @commands.command(name='glist', aliases=['giveawaylist'])
    @is_moderator()
    async def list_giveaways(self, ctx):
        active_giveaways = await self.db.get_active_giveaways()
        
        if not active_giveaways:
            embed = EmbedCreator.create_info_embed(
                "No Active Giveaways",
                "There are currently no active giveaways in this server."
            )
            await ctx.send(embed=embed)
            return
        
        embed = discord.Embed(
            title="🎉 Active Giveaways",
            color=0xFF69B4,
            timestamp=datetime.utcnow()
        )
        
        guild_giveaways = [g for g in active_giveaways if g['guild_id'] == ctx.guild.id]
        
        if not guild_giveaways:
            embed.description = "No active giveaways in this server."
        else:
            for giveaway in guild_giveaways[:10]:
                channel = ctx.guild.get_channel(giveaway['channel_id'])
                channel_name = channel.name if channel else "Unknown Channel"
                
                end_time = datetime.fromisoformat(giveaway['end_time'])
                end_timestamp = f"<t:{int(end_time.timestamp())}:R>"
                
                embed.add_field(
                    name=f"🎁 {giveaway['prize'][:50]}{'...' if len(giveaway['prize']) > 50 else ''}",
                    value=f"**Channel:** #{channel_name}\n**Ends:** {end_timestamp}\n**Winners:** {giveaway['winners']}\n**ID:** `{giveaway['message_id']}`",
                    inline=False
                )
        
        if len(guild_giveaways) > 10:
            embed.set_footer(text=f"Showing 10 of {len(guild_giveaways)} active giveaways")
        else:
            embed.set_footer(text=f"Total: {len(guild_giveaways)} active giveaways")
        
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(Giveaways(bot))
