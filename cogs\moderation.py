import discord
from discord.ext import commands
from datetime import datetime, timedelta
import asyncio
import re
from typing import Optional, Union

from utils.embeds import EmbedCreator
from utils.permissions import is_moderator, check_hierarchy, check_bot_permissions

class Moderation(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db

    @commands.command(name='ban')
    @is_moderator()
    async def ban_user(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await check_hierarchy(ctx, member):
            return

        if not await check_bot_permissions(ctx, ban_members=True):
            return

        try:
            await member.send(embed=EmbedCreator.create_warning_embed(
                "You have been banned",
                f"**Server:** {ctx.guild.name}\n**Reason:** {reason}\n**Moderator:** {ctx.author}"
            ))
        except:
            pass

        await member.ban(reason=f"{ctx.author}: {reason}")

        embed = EmbedCreator.create_moderation_embed(
            "ban", member, ctx.author, reason
        )
        await ctx.send(embed=embed)

        settings = await self.db.get_guild_settings(ctx.guild.id)
        if settings.get('mod_log_channel'):
            log_channel = ctx.guild.get_channel(settings['mod_log_channel'])
            if log_channel:
                await log_channel.send(embed=embed)

    @commands.command(name='kick')
    @is_moderator()
    async def kick_user(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await check_hierarchy(ctx, member):
            return

        if not await check_bot_permissions(ctx, kick_members=True):
            return

        try:
            await member.send(embed=EmbedCreator.create_warning_embed(
                "You have been kicked",
                f"**Server:** {ctx.guild.name}\n**Reason:** {reason}\n**Moderator:** {ctx.author}"
            ))
        except:
            pass

        await member.kick(reason=f"{ctx.author}: {reason}")

        embed = EmbedCreator.create_moderation_embed(
            "kick", member, ctx.author, reason
        )
        await ctx.send(embed=embed)

        settings = await self.db.get_guild_settings(ctx.guild.id)
        if settings.get('mod_log_channel'):
            log_channel = ctx.guild.get_channel(settings['mod_log_channel'])
            if log_channel:
                await log_channel.send(embed=embed)

    @commands.command(name='mute')
    @is_moderator()
    async def mute_user(self, ctx, member: discord.Member, duration: str = "1h", *, reason: str = "No reason provided"):
        if not await check_hierarchy(ctx, member):
            return

        if not await check_bot_permissions(ctx, moderate_members=True):
            return

        time_regex = re.match(r'(\d+)([smhd])', duration.lower())
        if not time_regex:
            embed = EmbedCreator.create_error_embed(
                "Invalid Duration",
                "Please use format: `1s`, `5m`, `2h`, `1d`"
            )
            await ctx.send(embed=embed)
            return

        amount, unit = time_regex.groups()
        amount = int(amount)

        if unit == 's':
            delta = timedelta(seconds=amount)
        elif unit == 'm':
            delta = timedelta(minutes=amount)
        elif unit == 'h':
            delta = timedelta(hours=amount)
        elif unit == 'd':
            delta = timedelta(days=amount)

        if delta > timedelta(days=28):
            embed = EmbedCreator.create_error_embed(
                "Duration Too Long",
                "Maximum mute duration is 28 days."
            )
            await ctx.send(embed=embed)
            return

        try:
            await member.timeout(delta, reason=f"{ctx.author}: {reason}")

            embed = EmbedCreator.create_moderation_embed(
                "mute", member, ctx.author, reason, duration
            )
            await ctx.send(embed=embed)

            settings = await self.db.get_guild_settings(ctx.guild.id)
            if settings.get('mod_log_channel'):
                log_channel = ctx.guild.get_channel(settings['mod_log_channel'])
                if log_channel:
                    await log_channel.send(embed=embed)

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I cannot mute this user due to role hierarchy or missing permissions."
            )
            await ctx.send(embed=embed)

    @commands.command(name='unmute')
    @is_moderator()
    async def unmute_user(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await check_hierarchy(ctx, member):
            return

        if not await check_bot_permissions(ctx, moderate_members=True):
            return

        if not member.is_timed_out():
            embed = EmbedCreator.create_error_embed(
                "User Not Muted",
                "This user is not currently muted."
            )
            await ctx.send(embed=embed)
            return

        try:
            await member.timeout(None, reason=f"{ctx.author}: {reason}")

            embed = EmbedCreator.create_moderation_embed(
                "unmute", member, ctx.author, reason
            )
            await ctx.send(embed=embed)

            settings = await self.db.get_guild_settings(ctx.guild.id)
            if settings.get('mod_log_channel'):
                log_channel = ctx.guild.get_channel(settings['mod_log_channel'])
                if log_channel:
                    await log_channel.send(embed=embed)

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I cannot unmute this user."
            )
            await ctx.send(embed=embed)

    @commands.command(name='warn')
    @is_moderator()
    async def warn_user(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await check_hierarchy(ctx, member):
            return

        await self.db.add_warning(ctx.guild.id, member.id, ctx.author.id, reason)

        try:
            await member.send(embed=EmbedCreator.create_warning_embed(
                "You have been warned",
                f"**Server:** {ctx.guild.name}\n**Reason:** {reason}\n**Moderator:** {ctx.author}"
            ))
        except:
            pass

        warnings = await self.db.get_warnings(ctx.guild.id, member.id)
        warning_count = len(warnings)

        embed = EmbedCreator.create_moderation_embed(
            "warn", member, ctx.author, reason
        )
        embed.add_field(name="⚠️ Total Warnings", value=str(warning_count), inline=True)

        await ctx.send(embed=embed)

        settings = await self.db.get_guild_settings(ctx.guild.id)
        if settings.get('mod_log_channel'):
            log_channel = ctx.guild.get_channel(settings['mod_log_channel'])
            if log_channel:
                await log_channel.send(embed=embed)

    @commands.command(name='warnings', aliases=['warns'])
    @is_moderator()
    async def view_warnings(self, ctx, member: discord.Member):
        warnings = await self.db.get_warnings(ctx.guild.id, member.id)

        if not warnings:
            embed = EmbedCreator.create_info_embed(
                "No Warnings",
                f"{member.mention} has no warnings."
            )
            await ctx.send(embed=embed)
            return

        embed = discord.Embed(
            title=f"⚠️ Warnings for {member.display_name}",
            color=0xFFFF00,
            timestamp=datetime.utcnow()
        )

        for i, warning in enumerate(warnings[:10], 1):
            moderator = ctx.guild.get_member(warning['moderator_id'])
            mod_name = moderator.display_name if moderator else "Unknown"

            embed.add_field(
                name=f"Warning #{warning['id']}",
                value=f"**Reason:** {warning['reason']}\n**Moderator:** {mod_name}\n**Date:** <t:{int(datetime.fromisoformat(warning['timestamp']).timestamp())}:R>",
                inline=False
            )

        if len(warnings) > 10:
            embed.set_footer(text=f"Showing 10 of {len(warnings)} warnings")
        else:
            embed.set_footer(text=f"Total: {len(warnings)} warnings")

        embed.set_thumbnail(url=member.display_avatar.url)
        await ctx.send(embed=embed)

    @commands.command(name='delwarn', aliases=['removewarn'])
    @is_moderator()
    async def delete_warning(self, ctx, warning_id: int):
        await self.db.remove_warning(warning_id)

        embed = EmbedCreator.create_success_embed(
            "Warning Removed",
            f"Warning #{warning_id} has been removed."
        )
        await ctx.send(embed=embed)

    @commands.command(name='unban')
    @is_moderator()
    async def unban_user(self, ctx, user_id: int, *, reason: str = "No reason provided"):
        if not await check_bot_permissions(ctx, ban_members=True):
            return

        try:
            user = await self.bot.fetch_user(user_id)
            await ctx.guild.unban(user, reason=f"{ctx.author}: {reason}")

            embed = EmbedCreator.create_moderation_embed(
                "unban", user, ctx.author, reason
            )
            await ctx.send(embed=embed)

            settings = await self.db.get_guild_settings(ctx.guild.id)
            if settings.get('mod_log_channel'):
                log_channel = ctx.guild.get_channel(settings['mod_log_channel'])
                if log_channel:
                    await log_channel.send(embed=embed)

        except discord.NotFound:
            embed = EmbedCreator.create_error_embed(
                "User Not Found",
                "This user is not banned or doesn't exist."
            )
            await ctx.send(embed=embed)

    @commands.command(name='softban')
    @is_moderator()
    async def softban_user(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await check_hierarchy(ctx, member):
            return

        if not await check_bot_permissions(ctx, ban_members=True):
            return

        try:
            await member.ban(reason=f"{ctx.author}: {reason} (Softban)", delete_message_days=7)
            await ctx.guild.unban(member, reason=f"{ctx.author}: Softban unban")

            embed = EmbedCreator.create_moderation_embed(
                "softban", member, ctx.author, reason
            )
            embed.add_field(name="📝 Note", value="User was banned and immediately unbanned to delete messages", inline=False)

            await ctx.send(embed=embed)

            settings = await self.db.get_guild_settings(ctx.guild.id)
            if settings.get('mod_log_channel'):
                log_channel = ctx.guild.get_channel(settings['mod_log_channel'])
                if log_channel:
                    await log_channel.send(embed=embed)

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I cannot ban this user."
            )
            await ctx.send(embed=embed)

    @commands.command(name='purge', aliases=['clear', 'clean'])
    @is_moderator()
    async def purge_messages(self, ctx, amount: int = 100):
        if not await check_bot_permissions(ctx, manage_messages=True, read_message_history=True):
            return

        if amount > 1000:
            embed = EmbedCreator.create_error_embed(
                "Amount Too Large",
                "Cannot purge more than 1000 messages at once."
            )
            await ctx.send(embed=embed)
            return

        try:
            deleted = await ctx.channel.purge(limit=amount + 1)

            embed = EmbedCreator.create_success_embed(
                "Messages Purged",
                f"Successfully deleted {len(deleted) - 1} messages."
            )

            msg = await ctx.send(embed=embed)
            await asyncio.sleep(3)
            await msg.delete()

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I don't have permission to delete messages in this channel."
            )
            await ctx.send(embed=embed)

    @commands.command(name='purgeuser')
    @is_moderator()
    async def purge_user_messages(self, ctx, member: discord.Member, amount: int = 100):
        if not await check_bot_permissions(ctx, manage_messages=True, read_message_history=True):
            return

        if amount > 1000:
            embed = EmbedCreator.create_error_embed(
                "Amount Too Large",
                "Cannot purge more than 1000 messages at once."
            )
            await ctx.send(embed=embed)
            return

        def check(message):
            return message.author == member

        try:
            deleted = await ctx.channel.purge(limit=amount, check=check)

            embed = EmbedCreator.create_success_embed(
                "User Messages Purged",
                f"Successfully deleted {len(deleted)} messages from {member.mention}."
            )

            msg = await ctx.send(embed=embed)
            await asyncio.sleep(3)
            await msg.delete()

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I don't have permission to delete messages in this channel."
            )
            await ctx.send(embed=embed)

    @commands.command(name='purgebots')
    @is_moderator()
    async def purge_bot_messages(self, ctx, amount: int = 100):
        if not await check_bot_permissions(ctx, manage_messages=True, read_message_history=True):
            return

        def check(message):
            return message.author.bot

        try:
            deleted = await ctx.channel.purge(limit=amount, check=check)

            embed = EmbedCreator.create_success_embed(
                "Bot Messages Purged",
                f"Successfully deleted {len(deleted)} bot messages."
            )

            msg = await ctx.send(embed=embed)
            await asyncio.sleep(3)
            await msg.delete()

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I don't have permission to delete messages in this channel."
            )
            await ctx.send(embed=embed)

    @commands.command(name='purgelinks')
    @is_moderator()
    async def purge_links(self, ctx, amount: int = 100):
        if not await check_bot_permissions(ctx, manage_messages=True, read_message_history=True):
            return

        def check(message):
            return 'http' in message.content.lower()

        try:
            deleted = await ctx.channel.purge(limit=amount, check=check)

            embed = EmbedCreator.create_success_embed(
                "Links Purged",
                f"Successfully deleted {len(deleted)} messages containing links."
            )

            msg = await ctx.send(embed=embed)
            await asyncio.sleep(3)
            await msg.delete()

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed(
                "Permission Denied",
                "I don't have permission to delete messages in this channel."
            )
            await ctx.send(embed=embed)

    @commands.command(name='lock')
    @is_moderator()
    async def lock_channel(self, ctx, channel: discord.TextChannel = None, *, reason: str = "No reason provided"):
        if not await check_bot_permissions(ctx, manage_channels=True):
            return

        channel = channel or ctx.channel

        overwrite = channel.overwrites_for(ctx.guild.default_role)
        overwrite.send_messages = False

        await channel.set_permissions(ctx.guild.default_role, overwrite=overwrite, reason=f"{ctx.author}: {reason}")

        embed = EmbedCreator.create_success_embed(
            "Channel Locked",
            f"🔒 {channel.mention} has been locked.\n**Reason:** {reason}"
        )
        await ctx.send(embed=embed)

    @commands.command(name='unlock')
    @is_moderator()
    async def unlock_channel(self, ctx, channel: discord.TextChannel = None, *, reason: str = "No reason provided"):
        if not await check_bot_permissions(ctx, manage_channels=True):
            return

        channel = channel or ctx.channel

        overwrite = channel.overwrites_for(ctx.guild.default_role)
        overwrite.send_messages = None

        await channel.set_permissions(ctx.guild.default_role, overwrite=overwrite, reason=f"{ctx.author}: {reason}")

        embed = EmbedCreator.create_success_embed(
            "Channel Unlocked",
            f"🔓 {channel.mention} has been unlocked.\n**Reason:** {reason}"
        )
        await ctx.send(embed=embed)

    @commands.command(name='lockdown')
    @is_moderator()
    async def lockdown_server(self, ctx, *, reason: str = "No reason provided"):
        if not await check_bot_permissions(ctx, manage_channels=True):
            return

        locked_channels = []

        for channel in ctx.guild.text_channels:
            try:
                overwrite = channel.overwrites_for(ctx.guild.default_role)
                if overwrite.send_messages is not False:
                    overwrite.send_messages = False
                    await channel.set_permissions(ctx.guild.default_role, overwrite=overwrite, reason=f"Lockdown by {ctx.author}: {reason}")
                    locked_channels.append(channel.mention)
            except:
                continue

        embed = EmbedCreator.create_success_embed(
            "Server Lockdown",
            f"🔒 Locked {len(locked_channels)} channels.\n**Reason:** {reason}"
        )
        await ctx.send(embed=embed)

    @commands.command(name='unlockdown')
    @is_moderator()
    async def unlock_server(self, ctx, *, reason: str = "No reason provided"):
        if not await check_bot_permissions(ctx, manage_channels=True):
            return

        unlocked_channels = []

        for channel in ctx.guild.text_channels:
            try:
                overwrite = channel.overwrites_for(ctx.guild.default_role)
                if overwrite.send_messages is False:
                    overwrite.send_messages = None
                    await channel.set_permissions(ctx.guild.default_role, overwrite=overwrite, reason=f"Unlock by {ctx.author}: {reason}")
                    unlocked_channels.append(channel.mention)
            except:
                continue

        embed = EmbedCreator.create_success_embed(
            "Server Unlocked",
            f"🔓 Unlocked {len(unlocked_channels)} channels.\n**Reason:** {reason}"
        )
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(Moderation(bot))
