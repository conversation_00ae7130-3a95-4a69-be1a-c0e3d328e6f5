import discord
from discord.ext import commands
from discord import app_commands
from datetime import datetime, timedelta
import asyncio
import re

from utils.embeds import EmbedCreator

class Moderation(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db

    @app_commands.command(name='ban', description='Ban a user from the server')
    @app_commands.describe(member='The member to ban', reason='Reason for the ban')
    async def ban_user(self, interaction: discord.Interaction, member: discord.Member, reason: str = "No reason provided"):
        if member.top_role >= interaction.guild.me.top_role:
            embed = EmbedCreator.create_error_embed("Permission Denied", "I cannot ban this user due to role hierarchy.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        if not interaction.guild.me.guild_permissions.ban_members:
            embed = EmbedCreator.create_error_embed("Missing Permissions", "I need ban members permission.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        try:
            await member.send(embed=EmbedCreator.create_warning_embed(
                "You have been banned",
                f"**Server:** {interaction.guild.name}\n**Reason:** {reason}\n**Moderator:** {interaction.user}"
            ))
        except:
            pass

        await member.ban(reason=f"{interaction.user}: {reason}")

        embed = EmbedCreator.create_moderation_embed("ban", member, interaction.user, reason)
        await interaction.response.send_message(embed=embed)

        settings = await self.db.get_guild_settings(interaction.guild.id)
        if settings.get('mod_log_channel'):
            log_channel = interaction.guild.get_channel(settings['mod_log_channel'])
            if log_channel:
                await log_channel.send(embed=embed)

    @app_commands.command(name='kick', description='Kick a user from the server')
    @app_commands.describe(member='The member to kick', reason='Reason for the kick')
    async def kick_user(self, interaction: discord.Interaction, member: discord.Member, reason: str = "No reason provided"):
        if member.top_role >= interaction.guild.me.top_role:
            embed = EmbedCreator.create_error_embed("Permission Denied", "I cannot kick this user due to role hierarchy.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        if not interaction.guild.me.guild_permissions.kick_members:
            embed = EmbedCreator.create_error_embed("Missing Permissions", "I need kick members permission.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        try:
            await member.send(embed=EmbedCreator.create_warning_embed(
                "You have been kicked",
                f"**Server:** {interaction.guild.name}\n**Reason:** {reason}\n**Moderator:** {interaction.user}"
            ))
        except:
            pass

        await member.kick(reason=f"{interaction.user}: {reason}")

        embed = EmbedCreator.create_moderation_embed("kick", member, interaction.user, reason)
        await interaction.response.send_message(embed=embed)

        settings = await self.db.get_guild_settings(interaction.guild.id)
        if settings.get('mod_log_channel'):
            log_channel = interaction.guild.get_channel(settings['mod_log_channel'])
            if log_channel:
                await log_channel.send(embed=embed)

    @app_commands.command(name='mute', description='Mute a user for a specified duration')
    @app_commands.describe(member='The member to mute', duration='Duration (1s, 5m, 2h, 1d)', reason='Reason for the mute')
    async def mute_user(self, interaction: discord.Interaction, member: discord.Member, duration: str = "1h", reason: str = "No reason provided"):
        if member.top_role >= interaction.guild.me.top_role:
            embed = EmbedCreator.create_error_embed("Permission Denied", "I cannot mute this user due to role hierarchy.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        if not interaction.guild.me.guild_permissions.moderate_members:
            embed = EmbedCreator.create_error_embed("Missing Permissions", "I need moderate members permission.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        time_regex = re.match(r'(\d+)([smhd])', duration.lower())
        if not time_regex:
            embed = EmbedCreator.create_error_embed("Invalid Duration", "Please use format: `1s`, `5m`, `2h`, `1d`")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        amount, unit = time_regex.groups()
        amount = int(amount)

        if unit == 's':
            delta = timedelta(seconds=amount)
        elif unit == 'm':
            delta = timedelta(minutes=amount)
        elif unit == 'h':
            delta = timedelta(hours=amount)
        elif unit == 'd':
            delta = timedelta(days=amount)

        if delta > timedelta(days=28):
            embed = EmbedCreator.create_error_embed("Duration Too Long", "Maximum mute duration is 28 days.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        try:
            await member.timeout(delta, reason=f"{interaction.user}: {reason}")
            embed = EmbedCreator.create_moderation_embed("mute", member, interaction.user, reason, duration)
            await interaction.response.send_message(embed=embed)

            settings = await self.db.get_guild_settings(interaction.guild.id)
            if settings.get('mod_log_channel'):
                log_channel = interaction.guild.get_channel(settings['mod_log_channel'])
                if log_channel:
                    await log_channel.send(embed=embed)

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed("Permission Denied", "I cannot mute this user.")
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name='unmute', description='Unmute a user')
    @app_commands.describe(member='The member to unmute', reason='Reason for the unmute')
    async def unmute_user(self, interaction: discord.Interaction, member: discord.Member, reason: str = "No reason provided"):
        if member.top_role >= interaction.guild.me.top_role:
            embed = EmbedCreator.create_error_embed("Permission Denied", "I cannot unmute this user due to role hierarchy.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        if not interaction.guild.me.guild_permissions.moderate_members:
            embed = EmbedCreator.create_error_embed("Missing Permissions", "I need moderate members permission.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        if not member.is_timed_out():
            embed = EmbedCreator.create_error_embed("User Not Muted", "This user is not currently muted.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        try:
            await member.timeout(None, reason=f"{interaction.user}: {reason}")
            embed = EmbedCreator.create_moderation_embed("unmute", member, interaction.user, reason)
            await interaction.response.send_message(embed=embed)

            settings = await self.db.get_guild_settings(interaction.guild.id)
            if settings.get('mod_log_channel'):
                log_channel = interaction.guild.get_channel(settings['mod_log_channel'])
                if log_channel:
                    await log_channel.send(embed=embed)

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed("Permission Denied", "I cannot unmute this user.")
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name='warn', description='Warn a user')
    @app_commands.describe(member='The member to warn', reason='Reason for the warning')
    async def warn_user(self, interaction: discord.Interaction, member: discord.Member, reason: str = "No reason provided"):
        if member.top_role >= interaction.guild.me.top_role:
            embed = EmbedCreator.create_error_embed("Permission Denied", "I cannot warn this user due to role hierarchy.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        await self.db.add_warning(interaction.guild.id, member.id, interaction.user.id, reason)

        try:
            await member.send(embed=EmbedCreator.create_warning_embed(
                "You have been warned",
                f"**Server:** {interaction.guild.name}\n**Reason:** {reason}\n**Moderator:** {interaction.user}"
            ))
        except:
            pass

        warnings = await self.db.get_warnings(interaction.guild.id, member.id)
        warning_count = len(warnings)

        embed = EmbedCreator.create_moderation_embed("warn", member, interaction.user, reason)
        embed.add_field(name="⚠️ Total Warnings", value=str(warning_count), inline=True)

        await interaction.response.send_message(embed=embed)

        settings = await self.db.get_guild_settings(interaction.guild.id)
        if settings.get('mod_log_channel'):
            log_channel = interaction.guild.get_channel(settings['mod_log_channel'])
            if log_channel:
                await log_channel.send(embed=embed)

    @app_commands.command(name='warnings', description='View warnings for a user')
    @app_commands.describe(member='The member to check warnings for')
    async def view_warnings(self, interaction: discord.Interaction, member: discord.Member):
        warnings = await self.db.get_warnings(interaction.guild.id, member.id)

        if not warnings:
            embed = EmbedCreator.create_info_embed("No Warnings", f"{member.mention} has no warnings.")
            await interaction.response.send_message(embed=embed)
            return

        embed = discord.Embed(
            title=f"⚠️ Warnings for {member.display_name}",
            color=0xFFFF00,
            timestamp=datetime.now()
        )

        for i, warning in enumerate(warnings[:10], 1):
            moderator = interaction.guild.get_member(warning['moderator_id'])
            mod_name = moderator.display_name if moderator else "Unknown"

            embed.add_field(
                name=f"Warning #{warning['id']}",
                value=f"**Reason:** {warning['reason']}\n**Moderator:** {mod_name}\n**Date:** <t:{int(datetime.fromisoformat(warning['timestamp']).timestamp())}:R>",
                inline=False
            )

        if len(warnings) > 10:
            embed.set_footer(text=f"Showing 10 of {len(warnings)} warnings")
        else:
            embed.set_footer(text=f"Total: {len(warnings)} warnings")

        embed.set_thumbnail(url=member.display_avatar.url)
        await interaction.response.send_message(embed=embed)

    @app_commands.command(name='delwarn', description='Delete a warning')
    @app_commands.describe(warning_id='The ID of the warning to delete')
    async def delete_warning(self, interaction: discord.Interaction, warning_id: int):
        await self.db.remove_warning(warning_id)
        embed = EmbedCreator.create_success_embed("Warning Removed", f"Warning #{warning_id} has been removed.")
        await interaction.response.send_message(embed=embed)

    @app_commands.command(name='purge', description='Delete multiple messages')
    @app_commands.describe(amount='Number of messages to delete (max 1000)')
    async def purge_messages(self, interaction: discord.Interaction, amount: int = 100):
        if not interaction.guild.me.guild_permissions.manage_messages:
            embed = EmbedCreator.create_error_embed("Missing Permissions", "I need manage messages permission.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        if amount > 1000:
            embed = EmbedCreator.create_error_embed("Amount Too Large", "Cannot purge more than 1000 messages at once.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        try:
            deleted = await interaction.channel.purge(limit=amount)
            embed = EmbedCreator.create_success_embed("Messages Purged", f"Successfully deleted {len(deleted)} messages.")
            await interaction.response.send_message(embed=embed, delete_after=5)

        except discord.Forbidden:
            embed = EmbedCreator.create_error_embed("Permission Denied", "I don't have permission to delete messages in this channel.")
            await interaction.response.send_message(embed=embed, ephemeral=True)

async def setup(bot):
    await bot.add_cog(Moderation(bot))
