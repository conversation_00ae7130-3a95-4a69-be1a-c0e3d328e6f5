import os
import sys
import asyncio

def check_requirements():
    try:
        import discord
        import aiosqlite
        import dotenv
    except ImportError:
        sys.exit(1)

def check_token():
    from dotenv import load_dotenv
    load_dotenv()

    token = os.getenv('DISCORD_TOKEN')
    if not token:
        sys.exit(1)

    return token

def main():
    check_requirements()
    check_token()

    try:
        from main import main as bot_main
        asyncio.run(bot_main())
    except KeyboardInterrupt:
        pass
    except Exception:
        sys.exit(1)

if __name__ == "__main__":
    main()
