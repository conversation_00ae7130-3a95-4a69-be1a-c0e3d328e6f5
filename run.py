import os
import sys
import asyncio

def check_requirements():
    try:
        import discord
        import aiosqlite
        import dotenv
    except ImportError as e:
        print(f"❌ Missing required package: {e.name}")
        print("📦 Please install requirements: pip install -r requirements.txt")
        sys.exit(1)

def check_token():
    from dotenv import load_dotenv
    load_dotenv()
    
    token = os.getenv('DISCORD_TOKEN')
    if not token:
        print("❌ Discord token not found!")
        print("📝 Please create a .env file with your DISCORD_TOKEN")
        print("💡 Copy .env.example to .env and add your token")
        sys.exit(1)
    
    return token

def main():
    print("🤖 Starting ModCore Discord Bot...")
    print("=" * 50)
    
    check_requirements()
    print("✅ All requirements satisfied")
    
    token = check_token()
    print("✅ Discord token found")
    
    print("🚀 Launching bot...")
    print("=" * 50)
    
    try:
        from main import main as bot_main
        asyncio.run(bot_main())
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Error starting bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
