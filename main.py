import discord
from discord.ext import commands, tasks
import async<PERSON>
from datetime import datetime
import traceback

from config import TOKEN, DEFAULT_PREFIX
from database.database import Database
from utils.embeds import EmbedCreator

class ModCore(commands.Bot):
    def __init__(self):
        intents = discord.Intents.all()
        super().__init__(
            command_prefix=self.get_prefix,
            intents=intents,
            help_command=None,
            case_insensitive=True,
            strip_after_prefix=True
        )

        self.db = Database('modcore.db')
        self.launch_time = datetime.now()

    async def get_prefix(self, message):
        if not message.guild:
            return DEFAULT_PREFIX

        settings = await self.db.get_guild_settings(message.guild.id)
        return settings.get('prefix', DEFAULT_PREFIX)

    async def setup_hook(self):
        await self.db.init_db()

        cogs_to_load = [
            'cogs.moderation',
            'cogs.automod',
            'cogs.roles',
            'cogs.utility',
            'cogs.giveaways',
            'cogs.custom_commands',
            'cogs.logging',
            'cogs.welcome',
            'cogs.starboard'
        ]

        for cog in cogs_to_load:
            try:
                await self.load_extension(cog)
            except Exception as e:
                traceback.print_exc()

        self.giveaway_checker.start()

    async def on_ready(self):
        await self.tree.sync()
        activity = discord.Activity(
            type=discord.ActivityType.watching,
            name=f"{len(self.guilds)} servers | /help"
        )
        await self.change_presence(activity=activity)

    async def on_guild_join(self, guild):
        embed = EmbedCreator.create_success_embed(
            "Thanks for adding ModCore!",
            f"Hello **{guild.name}**! 👋\n\n"
            f"I'm **ModCore**, a powerful moderation bot designed to keep your server safe and organized.\n\n"
            f"**Quick Setup:**\n"
            f"• Use `?help` to see all commands\n"
            f"• Set up automod with `?automod setup`\n"
            f"• Configure welcome messages with `?welcome setup`\n"
            f"• Create reaction roles with `?reactionrole`\n\n"
            f"**Need help?** Join our support server: [discord.gg/modcore](https://discord.gg/modcore)\n"
            f"**Documentation:** [docs.modcore.bot](https://docs.modcore.bot)"
        )

        embed.set_thumbnail(url=self.user.display_avatar.url)
        embed.add_field(
            name="🔧 Default Prefix",
            value="`?` (changeable with `?prefix`)",
            inline=True
        )
        embed.add_field(
            name="🛡️ Permissions",
            value="Make sure I have proper permissions!",
            inline=True
        )

        for channel in guild.text_channels:
            if channel.permissions_for(guild.me).send_messages:
                try:
                    await channel.send(embed=embed)
                    break
                except:
                    continue

    async def on_guild_remove(self, guild):
        pass

    async def on_command_error(self, ctx, error):
        if isinstance(error, commands.CommandNotFound):
            custom_response = await self.db.get_custom_command(ctx.guild.id, ctx.invoked_with)
            if custom_response:
                variables = {
                    '{user}': ctx.author.mention,
                    '{user.name}': ctx.author.display_name,
                    '{user.id}': str(ctx.author.id),
                    '{guild}': ctx.guild.name,
                    '{guild.id}': str(ctx.guild.id),
                    '{channel}': ctx.channel.mention,
                    '{channel.name}': ctx.channel.name,
                    '{channel.id}': str(ctx.channel.id)
                }

                for var, value in variables.items():
                    custom_response = custom_response.replace(var, value)

                await ctx.send(custom_response)
            return

        elif isinstance(error, commands.MissingPermissions):
            embed = EmbedCreator.create_error_embed(
                "Missing Permissions",
                f"You need the following permissions: {', '.join(error.missing_permissions)}"
            )
            await ctx.send(embed=embed)

        elif isinstance(error, commands.BotMissingPermissions):
            embed = EmbedCreator.create_error_embed(
                "Bot Missing Permissions",
                f"I need the following permissions: {', '.join(error.missing_permissions)}"
            )
            await ctx.send(embed=embed)

        elif isinstance(error, commands.MemberNotFound):
            embed = EmbedCreator.create_error_embed(
                "Member Not Found",
                "I couldn't find that member. Please check the username/ID and try again."
            )
            await ctx.send(embed=embed)

        elif isinstance(error, commands.RoleNotFound):
            embed = EmbedCreator.create_error_embed(
                "Role Not Found",
                "I couldn't find that role. Please check the role name/ID and try again."
            )
            await ctx.send(embed=embed)

        elif isinstance(error, commands.ChannelNotFound):
            embed = EmbedCreator.create_error_embed(
                "Channel Not Found",
                "I couldn't find that channel. Please check the channel name/ID and try again."
            )
            await ctx.send(embed=embed)

        elif isinstance(error, commands.BadArgument):
            embed = EmbedCreator.create_error_embed(
                "Invalid Argument",
                f"Invalid argument provided. Please check your command syntax.\n\n**Error:** {str(error)}"
            )
            await ctx.send(embed=embed)

        elif isinstance(error, commands.MissingRequiredArgument):
            embed = EmbedCreator.create_error_embed(
                "Missing Argument",
                f"Missing required argument: `{error.param.name}`\n\nUse `?help {ctx.command}` for more information."
            )
            await ctx.send(embed=embed)

        elif isinstance(error, commands.CommandOnCooldown):
            embed = EmbedCreator.create_warning_embed(
                "Command on Cooldown",
                f"This command is on cooldown. Try again in {error.retry_after:.2f} seconds."
            )
            await ctx.send(embed=embed)

        else:
            embed = EmbedCreator.create_error_embed(
                "Unexpected Error",
                f"An unexpected error occurred. Please try again later.\n\n**Error:** {str(error)}"
            )
            await ctx.send(embed=embed)
            traceback.print_exception(type(error), error, error.__traceback__)

    @tasks.loop(minutes=1)
    async def giveaway_checker(self):
        try:
            active_giveaways = await self.db.get_active_giveaways()

            for giveaway in active_giveaways:
                try:
                    guild = self.get_guild(giveaway['guild_id'])
                    if not guild:
                        continue

                    channel = guild.get_channel(giveaway['channel_id'])
                    if not channel:
                        continue

                    message = await channel.fetch_message(giveaway['message_id'])
                    if not message:
                        continue

                    reaction = discord.utils.get(message.reactions, emoji='🎉')
                    if not reaction:
                        continue

                    users = [user async for user in reaction.users() if not user.bot]

                    if len(users) < giveaway['winners']:
                        winners = users
                    else:
                        import random
                        winners = random.sample(users, giveaway['winners'])

                    if winners:
                        winner_mentions = ', '.join([user.mention for user in winners])
                        embed = EmbedCreator.create_success_embed(
                            "🎉 Giveaway Ended!",
                            f"**Prize:** {giveaway['prize']}\n**Winners:** {winner_mentions}"
                        )
                    else:
                        embed = EmbedCreator.create_warning_embed(
                            "🎉 Giveaway Ended!",
                            f"**Prize:** {giveaway['prize']}\n**Winners:** No valid entries"
                        )

                    await message.edit(embed=embed)
                    await channel.send(f"🎉 Giveaway ended! {winner_mentions if winners else 'No winners'}")

                    await self.db.end_giveaway(giveaway['message_id'])

                except Exception as e:
                    continue

        except Exception:
            pass

    @giveaway_checker.before_loop
    async def before_giveaway_checker(self):
        await self.wait_until_ready()

async def main():
    bot = ModCore()

    try:
        await bot.start(TOKEN)
    except KeyboardInterrupt:
        pass
    except Exception:
        pass
    finally:
        await bot.close()

if __name__ == '__main__':
    asyncio.run(main())
