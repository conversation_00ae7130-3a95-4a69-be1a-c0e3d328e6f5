import discord
from discord.ext import commands
from discord import app_commands
from datetime import datetime, timedelta
import asyncio
import random
import time

from utils.embeds import EmbedCreator

class Utility(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        self.start_time = time.time()
    
    @app_commands.command(name='help', description='Show help information')
    async def help_command(self, interaction: discord.Interaction):
        embed = discord.Embed(
            title="🤖 ModCore - Help Menu",
            description="A powerful Discord moderation bot with advanced features.",
            color=0x7289DA,
            timestamp=datetime.now()
        )
        
        embed.add_field(
            name="🔨 Moderation",
            value="`/ban`, `/kick`, `/mute`, `/warn`, `/purge`",
            inline=False
        )
        
        embed.add_field(
            name="🛡️ AutoMod",
            value="`/automod`, `/banword`, `/whitelist`",
            inline=False
        )
        
        embed.add_field(
            name="🎭 Roles",
            value="`/addrole`, `/removerole`, `/reactionrole`",
            inline=False
        )
        
        embed.add_field(
            name="🎉 Giveaways",
            value="`/giveaway`, `/gend`, `/greroll`",
            inline=False
        )
        
        embed.add_field(
            name="🔧 Utility",
            value="`/serverinfo`, `/userinfo`, `/avatar`, `/poll`",
            inline=False
        )
        
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)
        embed.set_footer(text="ModCore • Discord Bot")
        
        await interaction.response.send_message(embed=embed)
    
    @app_commands.command(name='serverinfo', description='Show server information')
    async def server_info(self, interaction: discord.Interaction):
        embed = EmbedCreator.create_server_info_embed(interaction.guild)
        await interaction.response.send_message(embed=embed)
    
    @app_commands.command(name='userinfo', description='Show user information')
    @app_commands.describe(member='The member to get info about')
    async def user_info(self, interaction: discord.Interaction, member: discord.Member = None):
        member = member or interaction.user
        embed = EmbedCreator.create_user_info_embed(member)
        await interaction.response.send_message(embed=embed)
    
    @app_commands.command(name='avatar', description='Show user avatar')
    @app_commands.describe(member='The member to show avatar for')
    async def avatar(self, interaction: discord.Interaction, member: discord.Member = None):
        member = member or interaction.user
        
        embed = discord.Embed(
            title=f"🖼️ {member.display_name}'s Avatar",
            color=member.color if member.color != discord.Color.default() else 0x7289DA,
            timestamp=datetime.now()
        )
        
        embed.set_image(url=member.display_avatar.url)
        embed.add_field(name="🔗 Avatar URL", value=f"[Click here]({member.display_avatar.url})", inline=True)
        embed.set_footer(text="ModCore • Avatar Display")
        
        await interaction.response.send_message(embed=embed)
    
    @app_commands.command(name='poll', description='Create a poll')
    @app_commands.describe(question='The poll question', option1='First option', option2='Second option', option3='Third option (optional)', option4='Fourth option (optional)')
    async def create_poll(self, interaction: discord.Interaction, question: str, option1: str, option2: str, option3: str = None, option4: str = None):
        options = [option1, option2]
        if option3:
            options.append(option3)
        if option4:
            options.append(option4)
        
        embed = discord.Embed(
            title="📊 Poll",
            description=f"**{question}**",
            color=0x00FF00,
            timestamp=datetime.now()
        )
        
        reactions = ['1️⃣', '2️⃣', '3️⃣', '4️⃣']
        
        for i, option in enumerate(options):
            embed.add_field(
                name=f"{reactions[i]} Option {i+1}",
                value=option,
                inline=False
            )
        
        embed.set_footer(text=f"Poll by {interaction.user.display_name}", icon_url=interaction.user.display_avatar.url)
        
        await interaction.response.send_message(embed=embed)
        poll_msg = await interaction.original_response()
        
        for i in range(len(options)):
            await poll_msg.add_reaction(reactions[i])
    
    @app_commands.command(name='flip', description='Flip a coin')
    async def flip_coin(self, interaction: discord.Interaction):
        result = random.choice(['Heads', 'Tails'])
        emoji = '🪙' if result == 'Heads' else '🥈'
        
        embed = discord.Embed(
            title=f"{emoji} Coin Flip",
            description=f"**Result: {result}**",
            color=0xFFD700,
            timestamp=datetime.now()
        )
        embed.set_footer(text=f"Flipped by {interaction.user.display_name}")
        
        await interaction.response.send_message(embed=embed)
    
    @app_commands.command(name='roll', description='Roll a dice')
    @app_commands.describe(sides='Number of sides on the dice (2-100)')
    async def roll_dice(self, interaction: discord.Interaction, sides: int = 6):
        if sides < 2 or sides > 100:
            embed = EmbedCreator.create_error_embed("Invalid Dice", "Dice must have between 2 and 100 sides.")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        result = random.randint(1, sides)
        
        embed = discord.Embed(
            title="🎲 Dice Roll",
            description=f"**Rolled a {result}** (1-{sides})",
            color=0x00FF00,
            timestamp=datetime.now()
        )
        embed.set_footer(text=f"Rolled by {interaction.user.display_name}")
        
        await interaction.response.send_message(embed=embed)
    
    @app_commands.command(name='ping', description='Check bot latency')
    async def ping(self, interaction: discord.Interaction):
        api_latency = round(self.bot.latency * 1000, 2)
        
        embed = discord.Embed(
            title="🏓 Pong!",
            color=0x00FF00,
            timestamp=datetime.now()
        )
        
        embed.add_field(name="📡 API Latency", value=f"{api_latency}ms", inline=True)
        
        if api_latency < 100:
            status = "🟢 Excellent"
        elif api_latency < 200:
            status = "🟡 Good"
        else:
            status = "🔴 Poor"
        
        embed.add_field(name="📊 Status", value=status, inline=True)
        embed.set_footer(text="ModCore • Latency Test")
        
        await interaction.response.send_message(embed=embed)
    
    @app_commands.command(name='uptime', description='Show bot uptime')
    async def uptime(self, interaction: discord.Interaction):
        current_time = time.time()
        uptime_seconds = int(current_time - self.start_time)
        
        days = uptime_seconds // 86400
        hours = (uptime_seconds % 86400) // 3600
        minutes = (uptime_seconds % 3600) // 60
        seconds = uptime_seconds % 60
        
        uptime_str = f"{days}d {hours}h {minutes}m {seconds}s"
        
        embed = discord.Embed(
            title="⏱️ Bot Uptime",
            description=f"**{uptime_str}**",
            color=0x00FF00,
            timestamp=datetime.now()
        )
        
        embed.add_field(name="🚀 Started", value=f"<t:{int(self.start_time)}:F>", inline=True)
        embed.add_field(name="📊 Guilds", value=str(len(self.bot.guilds)), inline=True)
        embed.add_field(name="👥 Users", value=str(len(self.bot.users)), inline=True)
        
        embed.set_footer(text="ModCore • System Information")
        await interaction.response.send_message(embed=embed)

async def setup(bot):
    await bot.add_cog(Utility(bot))
