import discord
from discord.ext import commands
from datetime import datetime, timedelta
import asyncio
import random
import time

from utils.embeds import EmbedCreator
from utils.permissions import is_moderator

class Utility(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        self.start_time = time.time()
    
    @commands.command(name='help')
    async def help_command(self, ctx, *, command: str = None):
        if command:
            cmd = self.bot.get_command(command)
            if not cmd:
                embed = EmbedCreator.create_error_embed(
                    "Command Not Found",
                    f"No command named `{command}` found."
                )
                await ctx.send(embed=embed)
                return
            
            embed = discord.Embed(
                title=f"🔧 Command: {cmd.name}",
                description=cmd.help or "No description available.",
                color=0x7289DA,
                timestamp=datetime.utcnow()
            )
            
            if cmd.aliases:
                embed.add_field(name="Aliases", value=', '.join(cmd.aliases), inline=True)
            
            embed.add_field(name="Usage", value=f"`?{cmd.name} {cmd.signature}`", inline=False)
            embed.set_footer(text="ModCore • Command Help")
            
            await ctx.send(embed=embed)
            return
        
        embed = discord.Embed(
            title="🤖 ModCore - Help Menu",
            description="A powerful Discord moderation bot with advanced features.",
            color=0x7289DA,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(
            name="🔨 Moderation",
            value="`ban`, `kick`, `mute`, `warn`, `purge`, `lock`, `lockdown`",
            inline=False
        )
        
        embed.add_field(
            name="🛡️ AutoMod",
            value="`automod`, `banword`, `whitelist`, `punishment`",
            inline=False
        )
        
        embed.add_field(
            name="🎭 Roles",
            value="`addrole`, `removerole`, `reactionrole`, `roleinfo`",
            inline=False
        )
        
        embed.add_field(
            name="🎉 Giveaways",
            value="`giveaway`, `gend`, `greroll`",
            inline=False
        )
        
        embed.add_field(
            name="🔧 Utility",
            value="`serverinfo`, `userinfo`, `avatar`, `poll`, `remind`",
            inline=False
        )
        
        embed.add_field(
            name="⭐ Starboard",
            value="`starboard`, `star`",
            inline=False
        )
        
        embed.add_field(
            name="👋 Welcome",
            value="`welcome`, `goodbye`",
            inline=False
        )
        
        embed.add_field(
            name="📝 Custom Commands",
            value="`addcommand`, `removecommand`, `commands`",
            inline=False
        )
        
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)
        embed.set_footer(text="Use ?help <command> for detailed information")
        
        await ctx.send(embed=embed)
    
    @commands.command(name='serverinfo', aliases=['si'])
    async def server_info(self, ctx):
        embed = EmbedCreator.create_server_info_embed(ctx.guild)
        await ctx.send(embed=embed)
    
    @commands.command(name='userinfo', aliases=['ui'])
    async def user_info(self, ctx, member: discord.Member = None):
        member = member or ctx.author
        embed = EmbedCreator.create_user_info_embed(member)
        await ctx.send(embed=embed)
    
    @commands.command(name='avatar', aliases=['av'])
    async def avatar(self, ctx, member: discord.Member = None):
        member = member or ctx.author
        
        embed = discord.Embed(
            title=f"🖼️ {member.display_name}'s Avatar",
            color=member.color if member.color != discord.Color.default() else 0x7289DA,
            timestamp=datetime.utcnow()
        )
        
        embed.set_image(url=member.display_avatar.url)
        embed.add_field(name="🔗 Avatar URL", value=f"[Click here]({member.display_avatar.url})", inline=True)
        embed.set_footer(text="ModCore • Avatar Display")
        
        await ctx.send(embed=embed)
    
    @commands.command(name='poll')
    async def create_poll(self, ctx, question: str, *options):
        if len(options) < 2:
            embed = EmbedCreator.create_error_embed(
                "Invalid Poll",
                "You need at least 2 options for a poll."
            )
            await ctx.send(embed=embed)
            return
        
        if len(options) > 10:
            embed = EmbedCreator.create_error_embed(
                "Too Many Options",
                "Maximum 10 options allowed."
            )
            await ctx.send(embed=embed)
            return
        
        embed = discord.Embed(
            title="📊 Poll",
            description=f"**{question}**",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        
        reactions = ['1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟']
        
        for i, option in enumerate(options):
            embed.add_field(
                name=f"{reactions[i]} Option {i+1}",
                value=option,
                inline=False
            )
        
        embed.set_footer(text=f"Poll by {ctx.author.display_name}", icon_url=ctx.author.display_avatar.url)
        
        poll_msg = await ctx.send(embed=embed)
        
        for i in range(len(options)):
            await poll_msg.add_reaction(reactions[i])
    
    @commands.command(name='remind', aliases=['reminder'])
    async def remind_me(self, ctx, time_str: str, *, reminder: str):
        time_regex = {
            's': 1, 'm': 60, 'h': 3600, 'd': 86400
        }
        
        time_amount = ''
        time_unit = ''
        
        for char in time_str:
            if char.isdigit():
                time_amount += char
            else:
                time_unit = char
                break
        
        if not time_amount or time_unit not in time_regex:
            embed = EmbedCreator.create_error_embed(
                "Invalid Time Format",
                "Use format: `1s`, `5m`, `2h`, `1d`"
            )
            await ctx.send(embed=embed)
            return
        
        seconds = int(time_amount) * time_regex[time_unit]
        
        if seconds > 86400 * 7:
            embed = EmbedCreator.create_error_embed(
                "Time Too Long",
                "Maximum reminder time is 7 days."
            )
            await ctx.send(embed=embed)
            return
        
        embed = EmbedCreator.create_success_embed(
            "Reminder Set",
            f"I'll remind you about: **{reminder}**\nIn: **{time_str}**"
        )
        await ctx.send(embed=embed)
        
        await asyncio.sleep(seconds)
        
        remind_embed = discord.Embed(
            title="⏰ Reminder",
            description=f"**{reminder}**",
            color=0xFFFF00,
            timestamp=datetime.utcnow()
        )
        remind_embed.set_footer(text=f"Reminder set {time_str} ago")
        
        try:
            await ctx.author.send(embed=remind_embed)
        except:
            await ctx.send(f"{ctx.author.mention}", embed=remind_embed)
    
    @commands.command(name='flip', aliases=['coinflip'])
    async def flip_coin(self, ctx):
        result = random.choice(['Heads', 'Tails'])
        emoji = '🪙' if result == 'Heads' else '🥈'
        
        embed = discord.Embed(
            title=f"{emoji} Coin Flip",
            description=f"**Result: {result}**",
            color=0xFFD700,
            timestamp=datetime.utcnow()
        )
        embed.set_footer(text=f"Flipped by {ctx.author.display_name}")
        
        await ctx.send(embed=embed)
    
    @commands.command(name='roll', aliases=['dice'])
    async def roll_dice(self, ctx, sides: int = 6):
        if sides < 2 or sides > 100:
            embed = EmbedCreator.create_error_embed(
                "Invalid Dice",
                "Dice must have between 2 and 100 sides."
            )
            await ctx.send(embed=embed)
            return
        
        result = random.randint(1, sides)
        
        embed = discord.Embed(
            title="🎲 Dice Roll",
            description=f"**Rolled a {result}** (1-{sides})",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        embed.set_footer(text=f"Rolled by {ctx.author.display_name}")
        
        await ctx.send(embed=embed)
    
    @commands.command(name='ping')
    async def ping(self, ctx):
        start_time = time.time()
        message = await ctx.send("🏓 Pinging...")
        end_time = time.time()
        
        api_latency = round(self.bot.latency * 1000, 2)
        message_latency = round((end_time - start_time) * 1000, 2)
        
        embed = discord.Embed(
            title="🏓 Pong!",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="📡 API Latency", value=f"{api_latency}ms", inline=True)
        embed.add_field(name="💬 Message Latency", value=f"{message_latency}ms", inline=True)
        
        if api_latency < 100:
            status = "🟢 Excellent"
        elif api_latency < 200:
            status = "🟡 Good"
        else:
            status = "🔴 Poor"
        
        embed.add_field(name="📊 Status", value=status, inline=True)
        embed.set_footer(text="ModCore • Latency Test")
        
        await message.edit(content=None, embed=embed)
    
    @commands.command(name='uptime')
    async def uptime(self, ctx):
        current_time = time.time()
        uptime_seconds = int(current_time - self.start_time)
        
        days = uptime_seconds // 86400
        hours = (uptime_seconds % 86400) // 3600
        minutes = (uptime_seconds % 3600) // 60
        seconds = uptime_seconds % 60
        
        uptime_str = f"{days}d {hours}h {minutes}m {seconds}s"
        
        embed = discord.Embed(
            title="⏱️ Bot Uptime",
            description=f"**{uptime_str}**",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(name="🚀 Started", value=f"<t:{int(self.start_time)}:F>", inline=True)
        embed.add_field(name="📊 Guilds", value=str(len(self.bot.guilds)), inline=True)
        embed.add_field(name="👥 Users", value=str(len(self.bot.users)), inline=True)
        
        embed.set_footer(text="ModCore • System Information")
        await ctx.send(embed=embed)
    
    @commands.command(name='prefix')
    @is_moderator()
    async def change_prefix(self, ctx, new_prefix: str = None):
        if not new_prefix:
            settings = await self.db.get_guild_settings(ctx.guild.id)
            current_prefix = settings.get('prefix', '?')
            
            embed = EmbedCreator.create_info_embed(
                "Current Prefix",
                f"The current prefix is: `{current_prefix}`\n\nUse `{current_prefix}prefix <new_prefix>` to change it."
            )
            await ctx.send(embed=embed)
            return
        
        if len(new_prefix) > 5:
            embed = EmbedCreator.create_error_embed(
                "Prefix Too Long",
                "Prefix cannot be longer than 5 characters."
            )
            await ctx.send(embed=embed)
            return
        
        await self.db.update_guild_setting(ctx.guild.id, 'prefix', new_prefix)
        
        embed = EmbedCreator.create_success_embed(
            "Prefix Updated",
            f"Server prefix changed to: `{new_prefix}`"
        )
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(Utility(bot))
