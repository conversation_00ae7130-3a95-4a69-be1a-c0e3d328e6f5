import discord
from discord.ext import commands
from datetime import datetime

from utils.embeds import EmbedCreator
from utils.permissions import is_moderator

class Welcome(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
    
    @commands.Cog.listener()
    async def on_member_join(self, member):
        settings = await self.db.get_guild_settings(member.guild.id)
        welcome_channel_id = settings.get('welcome_channel')
        
        if not welcome_channel_id:
            return
        
        welcome_channel = member.guild.get_channel(welcome_channel_id)
        if not welcome_channel:
            return
        
        custom_message = settings.get('welcome_message')
        
        embed = EmbedCreator.create_welcome_embed(member, member.guild, custom_message)
        
        try:
            await welcome_channel.send(embed=embed)
        except:
            pass
    
    @commands.Cog.listener()
    async def on_member_remove(self, member):
        settings = await self.db.get_guild_settings(member.guild.id)
        welcome_channel_id = settings.get('welcome_channel')
        
        if not welcome_channel_id:
            return
        
        welcome_channel = member.guild.get_channel(welcome_channel_id)
        if not welcome_channel:
            return
        
        custom_message = settings.get('goodbye_message')
        
        embed = EmbedCreator.create_goodbye_embed(member, member.guild, custom_message)
        
        try:
            await welcome_channel.send(embed=embed)
        except:
            pass
    
    @commands.group(name='welcome', invoke_without_command=True)
    @is_moderator()
    async def welcome(self, ctx):
        settings = await self.db.get_guild_settings(ctx.guild.id)
        
        embed = discord.Embed(
            title="👋 Welcome System",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        
        welcome_channel = None
        if settings.get('welcome_channel'):
            welcome_channel = ctx.guild.get_channel(settings['welcome_channel'])
        
        embed.add_field(
            name="📺 Welcome Channel",
            value=welcome_channel.mention if welcome_channel else "Not set",
            inline=True
        )
        
        embed.add_field(
            name="📝 Welcome Message",
            value="Custom" if settings.get('welcome_message') else "Default",
            inline=True
        )
        
        embed.add_field(
            name="👋 Goodbye Message",
            value="Custom" if settings.get('goodbye_message') else "Default",
            inline=True
        )
        
        embed.add_field(
            name="🔧 Commands",
            value="`?welcome channel <#channel>`\n`?welcome message <message>`\n`?welcome goodbye <message>`\n`?welcome test`\n`?welcome disable`",
            inline=False
        )
        
        embed.set_footer(text="ModCore • Welcome System")
        await ctx.send(embed=embed)
    
    @welcome.command(name='channel')
    @is_moderator()
    async def set_welcome_channel(self, ctx, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        
        await self.db.update_guild_setting(ctx.guild.id, 'welcome_channel', channel.id)
        
        embed = EmbedCreator.create_success_embed(
            "Welcome Channel Set",
            f"Welcome and goodbye messages will now be sent to {channel.mention}."
        )
        await ctx.send(embed=embed)
        
        test_embed = EmbedCreator.create_welcome_embed(ctx.author, ctx.guild)
        test_embed.title = "👋 Welcome System Test"
        test_embed.description = "This is a test message to confirm the welcome system is working properly."
        
        await channel.send(embed=test_embed)
    
    @welcome.command(name='message')
    @is_moderator()
    async def set_welcome_message(self, ctx, *, message: str):
        if len(message) > 1000:
            embed = EmbedCreator.create_error_embed(
                "Message Too Long",
                "Welcome messages cannot be longer than 1000 characters."
            )
            await ctx.send(embed=embed)
            return
        
        await self.db.update_guild_setting(ctx.guild.id, 'welcome_message', message)
        
        embed = EmbedCreator.create_success_embed(
            "Welcome Message Set",
            f"Custom welcome message has been set.\n\n**Preview:**\n{message[:200]}{'...' if len(message) > 200 else ''}"
        )
        
        embed.add_field(
            name="📝 Available Variables",
            value="`{user}` - Mentions the user\n`{user.name}` - User's name\n`{guild}` - Server name\n`{count}` - Member count",
            inline=False
        )
        
        await ctx.send(embed=embed)
    
    @welcome.command(name='goodbye')
    @is_moderator()
    async def set_goodbye_message(self, ctx, *, message: str):
        if len(message) > 1000:
            embed = EmbedCreator.create_error_embed(
                "Message Too Long",
                "Goodbye messages cannot be longer than 1000 characters."
            )
            await ctx.send(embed=embed)
            return
        
        await self.db.update_guild_setting(ctx.guild.id, 'goodbye_message', message)
        
        embed = EmbedCreator.create_success_embed(
            "Goodbye Message Set",
            f"Custom goodbye message has been set.\n\n**Preview:**\n{message[:200]}{'...' if len(message) > 200 else ''}"
        )
        
        embed.add_field(
            name="📝 Available Variables",
            value="`{user}` - User's name\n`{user.name}` - User's display name\n`{guild}` - Server name\n`{count}` - Member count",
            inline=False
        )
        
        await ctx.send(embed=embed)
    
    @welcome.command(name='test')
    @is_moderator()
    async def test_welcome(self, ctx):
        settings = await self.db.get_guild_settings(ctx.guild.id)
        welcome_channel_id = settings.get('welcome_channel')
        
        if not welcome_channel_id:
            embed = EmbedCreator.create_error_embed(
                "No Welcome Channel",
                "Please set a welcome channel first using `?welcome channel <#channel>`."
            )
            await ctx.send(embed=embed)
            return
        
        welcome_channel = ctx.guild.get_channel(welcome_channel_id)
        if not welcome_channel:
            embed = EmbedCreator.create_error_embed(
                "Invalid Channel",
                "The welcome channel no longer exists. Please set a new one."
            )
            await ctx.send(embed=embed)
            return
        
        custom_message = settings.get('welcome_message')
        
        test_embed = EmbedCreator.create_welcome_embed(ctx.author, ctx.guild, custom_message)
        test_embed.title = "👋 Welcome System Test"
        test_embed.description = f"**This is a test of the welcome system.**\n\n{test_embed.description}"
        
        await welcome_channel.send(embed=test_embed)
        
        confirmation_embed = EmbedCreator.create_success_embed(
            "Test Sent",
            f"Test welcome message sent to {welcome_channel.mention}."
        )
        await ctx.send(embed=confirmation_embed)
    
    @welcome.command(name='disable')
    @is_moderator()
    async def disable_welcome(self, ctx):
        await self.db.update_guild_setting(ctx.guild.id, 'welcome_channel', None)
        await self.db.update_guild_setting(ctx.guild.id, 'welcome_message', None)
        await self.db.update_guild_setting(ctx.guild.id, 'goodbye_message', None)
        
        embed = EmbedCreator.create_success_embed(
            "Welcome System Disabled",
            "Welcome and goodbye messages have been disabled."
        )
        await ctx.send(embed=embed)
    
    @welcome.command(name='reset')
    @is_moderator()
    async def reset_welcome_messages(self, ctx):
        await self.db.update_guild_setting(ctx.guild.id, 'welcome_message', None)
        await self.db.update_guild_setting(ctx.guild.id, 'goodbye_message', None)
        
        embed = EmbedCreator.create_success_embed(
            "Messages Reset",
            "Welcome and goodbye messages have been reset to default."
        )
        await ctx.send(embed=embed)
    
    @commands.command(name='greet')
    async def manual_greet(self, ctx, member: discord.Member = None):
        member = member or ctx.author
        
        settings = await self.db.get_guild_settings(ctx.guild.id)
        custom_message = settings.get('welcome_message')
        
        embed = EmbedCreator.create_welcome_embed(member, ctx.guild, custom_message)
        embed.title = "👋 Manual Greeting"
        
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(Welcome(bot))
